.custom-number-input {
  display: flex;
  flex-direction: column;
  margin: 0 15px;
}

.number-input-label {
  font-size: 0.85rem;
  margin-bottom: 4px;
  color: #555;
}

.number-input-container {
  display: flex;
  align-items: center;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 0;
  overflow: hidden;
}

.number-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border: none;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
  padding: 0;
}

.number-btn:hover {
  background-color: #e0e0e0;
}

.number-btn:active {
  background-color: #d0d0d0;
}

.number-btn:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

.number-input {
  width: 40px;
  height: 36px;
  border: none;
  text-align: center;
  font-size: 1rem;
  padding: 0;
  -moz-appearance: textfield; /* Firefox */
}

.number-input::-webkit-outer-spin-button,
.number-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.number-input:focus {
  outline: none;
}

/* Mobile styles */
@media (max-width: 480px) {
  .custom-number-input {
    margin: 0 8px;
  }
  
  .number-input-container {
    height: 32px;
  }
  
  .number-btn {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
  
  .number-input {
    width: 36px;
    height: 32px;
    font-size: 0.9rem;
  }
}
