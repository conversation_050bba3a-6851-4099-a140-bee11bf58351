/* Auth Page Layout */
.auth-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 20px;
  background-color: #f9f9f9;
}

.auth-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  transition: all 0.3s ease;
}

/* Animation classes */
.form-fade-out {
  opacity: 0.5;
  transform: translateY(10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.form-fade-in {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Header Styles */
.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  margin: 0 0 10px;
  color: #1976D2;
  font-size: 28px;
  font-weight: 600;
}

.auth-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

/* Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 0;
}

.form-group label {
  font-weight: 500;
  color: #555;
  font-size: 15px;
  display: block;
  margin-bottom: 0;
}

.form-group input {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  width: 100%;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus {
  border-color: #1976D2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* Error Message */
.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 4px;
  margin: 0 0 20px 0;
  border-left: 4px solid #c62828;
  font-size: 14px;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 15px;
  border-radius: 4px;
  margin: 0 0 20px 0;
  border-left: 4px solid #2e7d32;
  text-align: center;
  font-size: 14px;
}

/* Button Styles */
.auth-container .auth-button {
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  text-transform: uppercase;
  letter-spacing: 1px;
  width: 100%;
  margin-top: 0;
}

.auth-container .auth-button:hover {
  background-color: #1565C0;
}

.auth-container .auth-button:disabled {
  background-color: #90CAF9;
  cursor: not-allowed;
}

/* Footer Styles */
.auth-container .auth-footer {
  text-align: center;
  margin-top: 20px;
  color: #666;
  font-size: 15px;
}

.auth-container .switch-button {
  background: none;
  border: none;
  color: #1976D2;
  font-weight: 600;
  cursor: pointer;
  padding: 5px 10px;
  font-size: 15px;
  margin-left: 8px;
  transition: color 0.3s;
  text-decoration: none;
  width: auto;
}

.auth-container .switch-button:hover {
  color: #1565C0;
  text-decoration: underline;
  background: none;
}

/* Forgot Password Styles */
.auth-container .forgot-password {
  text-align: right;
  margin: 10px 0 20px;
}

.auth-container .forgot-password-link {
  background: none;
  border: none;
  color: #1976D2;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  margin: 0;
  width: auto;
  font-weight: 500;
  transition: color 0.3s;
}

.auth-container .forgot-password-link:hover {
  color: #1565C0;
  text-decoration: underline;
  background: none;
}
