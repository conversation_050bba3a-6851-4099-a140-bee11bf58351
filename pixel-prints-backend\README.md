# Pixel Prints - Backend API

This is the backend API for Pixel Prints, a professional document and photo printing service with delivery across Luzon.

## Deployment Instructions for Railway

### Prerequisites
- A Railway account
- Git repository with your code

### Deployment Steps

1. **Push your code to a Git repository**
   - Create a repository on GitHub, GitLab, or Bitbucket
   - Push your code to the repository

2. **Connect to Railway**
   - Log in to your Railway account
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Select your repository

3. **Configure environment variables**
   - Go to the "Variables" tab
   - Add the following environment variables:
     - `PORT`: 3001 (or any port Railway assigns)
     - `NODE_ENV`: production
     - `FRONTEND_URL`: Your Netlify frontend URL
     - `DATABASE_URL`: This will be automatically set by Railway if you add a PostgreSQL database
     - `EMAIL_USER`: <EMAIL>
     - `EMAIL_PASS`: Your email app password
     - `EMAIL_HOST`: smtp.gmail.com
     - `EMAIL_PORT`: 587
     - `JWT_SECRET`: A secure random string for JWT token generation
     - `ADMIN_EMAIL`: Admin email address
     - `ADMIN_PASSWORD`: Admin password

4. **Add a PostgreSQL database**
   - Click "New" and select "Database"
   - Choose "PostgreSQL"
   - Railway will automatically add the connection string as `DATABASE_URL`

5. **Deploy the application**
   - Railway will automatically deploy your application
   - The postinstall script will initialize the database

6. **Set up custom domain (optional)**
   - Go to "Settings" > "Domains"
   - Click "Generate Domain" or "Custom Domain"
   - Follow the instructions to set up your domain

## Local Development

1. Clone the repository
2. Install dependencies: `npm install`
3. Create a `.env` file with the required environment variables (see `.env.example`)
4. Initialize the database: `npm run init-db`
5. Set up settings: `npm run setup-settings`
6. Create admin user: `npm run create-admin`
7. Start the development server: `npm run dev`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### Orders
- `GET /api/orders` - Get all orders (admin only)
- `POST /send-order` - Create a new order
- `PUT /api/orders/:id/status` - Update order status (admin only)
- `DELETE /api/orders/:id` - Delete an order (admin only)

### Settings
- `GET /api/settings` - Get all settings
- `PUT /api/settings/:key` - Update a setting (admin only)
- `GET /api/settings/daily-order-limit/check` - Check if daily order limit is reached

### Users
- `GET /api/users/admin-dashboard` - Get all users (admin only)

## Technologies Used

- Node.js
- Express
- PostgreSQL
- Multer for file uploads
- Nodemailer for email sending
- JWT for authentication
