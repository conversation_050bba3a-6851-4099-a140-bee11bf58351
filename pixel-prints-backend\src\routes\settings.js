const express = require('express');
const router = express.Router();
const {
  getAllSettings,
  getSettingByKey,
  updateSetting,
  checkDailyOrderLimit
} = require('../controllers/settings');

// Public route to check if daily order limit is reached
router.get('/daily-order-limit/check', checkDailyOrderLimit);

// Routes for managing settings
router.get('/', getAllSettings);
router.get('/:key', getSettingByKey);
router.put('/:key', updateSetting);

module.exports = router;
