[build]
  command = "npm run build"
  publish = "build"

[build.environment]
  REACT_APP_API_URL = "https://pixel-prints-1.onrender.com"
  REACT_APP_VERSION = "1.0.0"
  REACT_APP_ENABLE_ANALYTICS = "true"
  REACT_APP_ENABLE_NOTIFICATIONS = "true"
  REACT_APP_APP_NAME = "Pixel Prints"
  REACT_APP_COMPANY_NAME = "Pixel Prints"
  REACT_APP_CONTACT_EMAIL = "<EMAIL>"
  REACT_APP_CONTACT_PHONE = "+************"
  # Prevent warnings from being treated as errors
  CI = "false"

# Handle SPA routing by redirecting all requests to index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Set custom headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; connect-src 'self' https://*.vercel.app https://*.supabase.co https://*.railway.app https://*.up.railway.app https://pixel-prints-production.up.railway.app https://pixel-prints-1.onrender.com https://pixelprints.it.com; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval';"
