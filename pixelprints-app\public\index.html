<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.svg" type="image/svg+xml" />
    <link rel="alternate icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#2E7D32" />
    <meta
      name="description"
      content="Pixel Prints - Professional printing services for documents, photos, and more. Fast delivery across Luzon. Upload your files and get high-quality prints delivered to your doorstep."
    />
    <meta name="keywords" content="printing services, document printing, photo printing, print delivery, PDF printing, online printing, Luzon printing service, Pixel Prints" />
    <meta name="author" content="Pixel Prints" />
    <meta property="og:title" content="Pixel Prints - Professional Printing Services" />
    <meta property="og:description" content="Upload your files and get high-quality prints delivered to your doorstep. Fast delivery across Luzon." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://pixelprints.com" />
    <meta property="og:image" content="%PUBLIC_URL%/favicon.svg" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Pixel Prints - Professional Printing Services" />
    <meta name="twitter:description" content="Upload your files and get high-quality prints delivered to your doorstep. Fast delivery across Luzon." />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/favicon.svg" />
    <!-- Google Fonts for T-Shirt Designer -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bangers&family=Bebas+Neue&family=Fredoka+One&family=Lato:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;700&family=Pacifico&family=Permanent+Marker&family=Poppins:wght@400;700&family=Raleway:wght@400;700&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Pixel Prints | Professional Document & Photo Printing Services</title>

    <!-- Structured data for SEO -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Pixel Prints",
      "image": "%PUBLIC_URL%/favicon.svg",
      "description": "Professional printing services for documents, photos, and more. Fast delivery across Luzon.",
      "url": "https://pixelprints.com",
      "telephone": "+************",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Manila",
        "addressRegion": "NCR",
        "addressCountry": "PH"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "14.5995",
        "longitude": "120.9842"
      },
      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday"
        ],
        "opens": "09:00",
        "closes": "18:00"
      },
      "priceRange": "₱₱",
      "servesCuisine": "Printing Services",
      "sameAs": [
        "https://www.facebook.com/pixelprints",
        "https://www.instagram.com/pixelprints"
      ]
    }
    </script>

    <!-- Service structured data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Service",
      "serviceType": "Document and Photo Printing",
      "provider": {
        "@type": "LocalBusiness",
        "name": "Pixel Prints"
      },
      "areaServed": {
        "@type": "Country",
        "name": "Philippines"
      },
      "description": "Professional printing services for documents, photos, and more with fast delivery across Luzon.",
      "offers": {
        "@type": "Offer",
        "price": "5.00",
        "priceCurrency": "PHP",
        "description": "Black & White printing starting at ₱5.00 per page"
      }
    }
    </script>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
