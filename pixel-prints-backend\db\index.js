const { Pool } = require('pg');
require('dotenv').config();

// Use DATABASE_URL if available (for production), otherwise use config file
let pool;
if (process.env.DATABASE_URL) {
  pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });
} else {
  const dbConfig = require('../config/db.config.js');
  pool = new Pool({
    user: dbConfig.USER,
    host: dbConfig.HOST,
    database: dbConfig.DB,
    password: dbConfig.PASSWORD,
    port: dbConfig.port,
  });
}

module.exports = {
  query: (text, params) => pool.query(text, params),
};