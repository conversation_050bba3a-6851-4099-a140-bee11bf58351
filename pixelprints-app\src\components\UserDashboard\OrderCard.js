import React from 'react';
import './OrderCard.css';

const OrderCard = ({ order }) => {
  console.log('OrderCard received order:', order);
  const getStatusClass = (status) => {
    switch (status) {
      case 'pending':
        return 'status-pending';
      case 'processing':
        return 'status-processing';
      case 'completed':
        return 'status-completed';
      case 'cancelled':
        return 'status-cancelled';
      default:
        return 'status-default';
    }
  };

  // Format date to be more readable
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      return new Date(dateString).toLocaleDateString(undefined, options);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  return (
    <div className="order-card">
      <div className="order-card-header">
        <div className="order-info">
          <h3 className="order-number">Order #{order.order_number || order.id || 'Unknown'}</h3>
          <p className="order-date">{formatDate(order.created_at)}</p>
        </div>
        <span className={`status-badge ${getStatusClass(order.status || 'pending')}`}>
          {order.status ? order.status.charAt(0).toUpperCase() + order.status.slice(1) : 'Pending'}
        </span>
      </div>

      <div className="order-details">
        <div className="detail-group">
          <p className="detail-label">Print Type</p>
          <p className="detail-value">
            {order.print_type ? (order.print_type === 'black' ? 'Black & White' : 'Colored') : 'N/A'}
          </p>
        </div>
        <div className="detail-group">
          <p className="detail-label">Total Pages</p>
          <p className="detail-value">{order.total_pages || 'N/A'}</p>
        </div>
      </div>

      <div className="order-footer">
        <div className="price-container">
          <span className="price-label">Total Amount:</span>
          <span className="price-value">
            ₱{order.price ? parseFloat(order.price).toFixed(2) : '0.00'}
          </span>
        </div>
        <div className="view-details-hint">
          <span>Click to view details</span>
        </div>
      </div>
    </div>
  );
};

export default OrderCard;
