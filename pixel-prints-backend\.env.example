# Server Configuration
PORT=3001
NODE_ENV=production

# Frontend URL for CORS (comma-separated list for multiple domains)
FRONTEND_URL=https://pixelprints.it.com,https://pixel-prints.netlify.app

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/pixelprints

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587

# JWT Secret for Authentication
JWT_SECRET=your-jwt-secret-key

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-admin-password
