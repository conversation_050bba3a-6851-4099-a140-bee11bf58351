const express = require('express');
const router = express.Router();
const {
  getAllUsers,
  getCustomersFromOrders,
  getAllUsersAndCustomers,
  createUser,
  updateUser,
  deleteUser,
  getUserById
} = require('../controllers/users');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Get all users (protected, admin only)
router.get('/', authenticateToken, isAdmin, getAllUsers);

// Get all customers from orders (protected, admin only)
router.get('/customers', authenticateToken, isAdmin, getCustomersFromOrders);

// Get all users and customers combined (protected, admin only)
router.get('/all', authenticateToken, isAdmin, getAllUsersAndCustomers);

// Public endpoint for admin dashboard (in a real app, this would be protected)
router.get('/admin-dashboard', getAllUsersAndCustomers);

// Get a single user by ID
router.get('/:id', authenticateToken, isAdmin, getUserById);

// Create a new user
router.post('/', authenticateToken, isAdmin, createUser);

// Update a user
router.put('/:id', authenticateToken, isAdmin, updateUser);

// Delete a user
router.delete('/:id', authenticateToken, isAdmin, deleteUser);

// Public endpoints for admin dashboard (in a real app, these would be protected)
router.post('/admin-dashboard', createUser);
router.put('/admin-dashboard/:id', updateUser);
router.delete('/admin-dashboard/:id', deleteUser);

module.exports = router;
