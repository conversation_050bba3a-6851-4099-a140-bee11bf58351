const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Use DATABASE_URL if available (for production), otherwise use config file
let pool;
if (process.env.DATABASE_URL) {
  pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });
} else {
  const dbConfig = require('../config/db.config.js');
  pool = new Pool({
    user: dbConfig.USER,
    host: dbConfig.HOST,
    password: dbConfig.PASSWORD,
    port: dbConfig.port,
    database: dbConfig.DB
  });
}

async function setupSettings() {
  try {
    // Read the SQL file
    const sqlFile = path.join(__dirname, 'create_settings_table.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    // Execute the SQL
    await pool.query(sql);
    console.log('Settings table initialized successfully');
  } catch (error) {
    console.error('Error initializing settings table:', error);
  } finally {
    await pool.end();
  }
}

setupSettings();
