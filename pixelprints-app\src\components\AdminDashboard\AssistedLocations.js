import React, { useState, useEffect, useRef } from 'react';
import './AssistedLocations.css';

// Custom checkbox component that handles indeterminate state
const IndeterminateCheckbox = ({ id, checked, indeterminate, onChange, label }) => {
  const checkboxRef = useRef(null);

  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  const handleClick = (e) => {
    // Prevent event bubbling
    e.stopPropagation();

    // Call the onChange handler
    onChange(e);
  };

  return (
    <div className="checkbox-container">
      <input
        ref={checkboxRef}
        type="checkbox"
        id={id}
        checked={checked}
        onChange={handleClick}
      />
      <label htmlFor={id} onClick={handleClick}>{label}</label>
    </div>
  );
};

const AssistedLocations = () => {
  // State for storing all regions and provinces
  const [regionData, setRegionData] = useState({});

  // State for storing assisted locations
  const [assistedLocations, setAssistedLocations] = useState({});

  // State for UI
  const [loading, setLoading] = useState(true);
  const [saveStatus, setSaveStatus] = useState('');
  const [expandedRegions, setExpandedRegions] = useState({});

  // Structured list of Philippines regions and provinces
  const philippinesRegions = {
    "Northern Luzon (Regions I, II, CAR)": {
      "Region I (Ilocos Region)": ["Ilocos Norte", "Ilocos Sur", "La Union", "Pangasinan"],
      "Region II (Cagayan Valley)": ["Batanes", "Cagayan", "Isabela", "Nueva Vizcaya", "Quirino"],
      "CAR (Cordillera Administrative Region)": ["Abra", "Apayao", "Benguet", "Ifugao", "Kalinga", "Mountain Province"],
    },
    "Central Luzon (Region III)": {
      "Region III": ["Aurora", "Bataan", "Bulacan", "Nueva Ecija", "Pampanga", "Tarlac", "Zambales"],
    },
    "Southern Tagalog (Regions IV-A, IV-B, NCR)": {
      "Region IV-A (CALABARZON)": ["Batangas", "Cavite", "Laguna", "Quezon", "Rizal"],
      "Region IV-B (MIMAROPA)": ["Marinduque", "Occidental Mindoro", "Oriental Mindoro", "Palawan", "Romblon"],
      "NCR (National Capital Region)": [
        "Manila", "Caloocan", "Las Piñas", "Makati", "Malabon", "Mandaluyong", "Marikina",
        "Muntinlupa", "Navotas", "Parañaque", "Pasay", "Pasig", "Pateros", "Quezon City",
        "San Juan", "Taguig", "Valenzuela",
      ],
    },
    "Bicol Region (Region V)": {
      "Region V": ["Albay", "Camarines Norte", "Camarines Sur", "Catanduanes", "Masbate", "Sorsogon"],
    },
    "Visayas (Regions VI, VII, VIII)": {
      "Region VI (Western Visayas)": ["Aklan", "Antique", "Capiz", "Guimaras", "Iloilo", "Negros Occidental"],
      "Region VII (Central Visayas)": ["Bohol", "Cebu", "Negros Oriental", "Siquijor"],
      "Region VIII (Eastern Visayas)": ["Biliran", "Eastern Samar", "Leyte", "Northern Samar", "Samar", "Southern Leyte"],
    },
    "Mindanao (Regions IX, X, XI, XII, XIII, BARMM)": {
      "Region IX (Zamboanga Peninsula)": ["Zamboanga del Norte", "Zamboanga del Sur", "Zamboanga Sibugay", "Isabela City", "Zamboanga City"],
      "Region X (Northern Mindanao)": ["Bukidnon", "Camiguin", "Lanao del Norte", "Misamis Occidental", "Misamis Oriental"],
      "Region XI (Davao Region)": ["Davao de Oro", "Davao del Norte", "Davao del Sur", "Davao Occidental", "Davao Oriental"],
      "Region XII (SOCCSKSARGEN)": ["Cotabato", "Sarangani", "South Cotabato", "Sultan Kudarat", "General Santos City"],
      "Region XIII (Caraga)": ["Agusan del Norte", "Agusan del Sur", "Dinagat Islands", "Surigao del Norte", "Surigao del Sur"],
      "BARMM (Bangsamoro)": ["Basilan", "Lanao del Sur", "Maguindanao", "Sulu", "Tawi-Tawi"],
    },
  };

  // Initialize data on component mount
  useEffect(() => {
    // Set the region data
    setRegionData(philippinesRegions);

    // Load assisted locations from localStorage
    const savedLocations = localStorage.getItem('assistedLocations');
    if (savedLocations) {
      setAssistedLocations(JSON.parse(savedLocations));
    } else {
      // Initialize with empty structure
      const initialAssistedLocations = {};
      Object.keys(philippinesRegions).forEach(regionGroup => {
        initialAssistedLocations[regionGroup] = {};
        Object.keys(philippinesRegions[regionGroup]).forEach(region => {
          initialAssistedLocations[regionGroup][region] = {};
          philippinesRegions[regionGroup][region].forEach(province => {
            initialAssistedLocations[regionGroup][region][province] = false;
          });
        });
      });
      setAssistedLocations(initialAssistedLocations);
    }

    setLoading(false);
  }, []);

  // Toggle a province's assisted status - optimized to avoid deep cloning the entire state
  const toggleProvince = (regionGroup, region, province) => {
    setAssistedLocations(prev => {
      // Create a new object with the same structure but only clone the parts we need to change
      // This is more efficient than deep cloning the entire state object
      return {
        ...prev,
        [regionGroup]: {
          ...prev[regionGroup],
          [region]: {
            ...prev[regionGroup]?.[region],
            [province]: !(prev[regionGroup]?.[region]?.[province] || false)
          }
        }
      };
    });
  };

  // Toggle all provinces in a region - optimized version
  const toggleRegion = (regionGroup, region) => {
    setAssistedLocations(prev => {
      // Get all provinces in this region
      const allProvinces = regionData[regionGroup][region];

      // Check if all provinces are currently checked
      const allChecked = allProvinces.every(province =>
        prev[regionGroup]?.[region]?.[province]
      );

      // Create a new region object with all provinces toggled
      const newRegionState = {};
      allProvinces.forEach(province => {
        newRegionState[province] = !allChecked;
      });

      // Return new state with only the changed parts
      return {
        ...prev,
        [regionGroup]: {
          ...prev[regionGroup],
          [region]: newRegionState
        }
      };
    });
  };

  // Toggle all regions in a region group - optimized version
  const toggleRegionGroup = (regionGroup) => {
    setAssistedLocations(prev => {
      const regions = Object.keys(regionData[regionGroup]);

      // Check if all regions are fully selected
      const allRegionsChecked = regions.every(region => {
        const provinces = regionData[regionGroup][region];
        return provinces.every(province =>
          prev[regionGroup]?.[region]?.[province]
        );
      });

      // Create a new region group object with all provinces toggled
      const newRegionGroupState = {};

      // For each region in the group
      regions.forEach(region => {
        // Create a new region object
        const newRegionState = {};

        // Set all provinces in this region to the new value
        regionData[regionGroup][region].forEach(province => {
          newRegionState[province] = !allRegionsChecked;
        });

        // Add this region to the new region group state
        newRegionGroupState[region] = newRegionState;
      });

      // Return new state with only the changed parts
      return {
        ...prev,
        [regionGroup]: newRegionGroupState
      };
    });
  };

  // Toggle expansion of a region group
  const toggleExpand = (regionGroup) => {
    setExpandedRegions(prev => ({
      ...prev,
      [regionGroup]: !prev[regionGroup]
    }));
  };

  // Save assisted locations to localStorage
  const saveLocations = () => {
    // Save to localStorage
    localStorage.setItem('assistedLocations', JSON.stringify(assistedLocations));

    // Dispatch a storage event to notify other components
    window.dispatchEvent(new Event('storage'));

    // Show success message
    setSaveStatus('Locations saved successfully!');

    // Clear status message after 3 seconds
    setTimeout(() => {
      setSaveStatus('');
    }, 3000);

    console.log('Saved assisted locations to localStorage');
  };

  // Memoized function to check if all provinces in a region are selected
  const isRegionFullySelected = (() => {
    const cache = new Map();

    return (regionGroup, region) => {
      // Create a cache key
      const cacheKey = `${regionGroup}-${region}`;

      // If we have a cached result and the locations haven't changed, use it
      if (cache.has(cacheKey) && cache.get(cacheKey).locations === assistedLocations) {
        return cache.get(cacheKey).result;
      }

      // Otherwise compute the result
      if (!assistedLocations[regionGroup] || !assistedLocations[regionGroup][region]) {
        const result = false;
        cache.set(cacheKey, { result, locations: assistedLocations });
        return result;
      }

      const result = regionData[regionGroup][region].every(province =>
        assistedLocations[regionGroup][region][province]
      );

      // Cache the result
      cache.set(cacheKey, { result, locations: assistedLocations });
      return result;
    };
  })();

  // Memoized function to check if any province in a region is selected
  const isRegionPartiallySelected = (() => {
    const cache = new Map();

    return (regionGroup, region) => {
      // Create a cache key
      const cacheKey = `${regionGroup}-${region}`;

      // If we have a cached result and the locations haven't changed, use it
      if (cache.has(cacheKey) && cache.get(cacheKey).locations === assistedLocations) {
        return cache.get(cacheKey).result;
      }

      // Otherwise compute the result
      if (!assistedLocations[regionGroup] || !assistedLocations[regionGroup][region]) {
        const result = false;
        cache.set(cacheKey, { result, locations: assistedLocations });
        return result;
      }

      const provinces = regionData[regionGroup][region];
      let selectedCount = 0;

      // More efficient counting without creating intermediate arrays
      for (const province of provinces) {
        if (assistedLocations[regionGroup][region][province]) {
          selectedCount++;
        }
      }

      const result = selectedCount > 0 && selectedCount < provinces.length;

      // Cache the result
      cache.set(cacheKey, { result, locations: assistedLocations });
      return result;
    };
  })();

  // Memoized function to check if all regions in a region group are selected
  const isRegionGroupFullySelected = (() => {
    const cache = new Map();

    return (regionGroup) => {
      // If we have a cached result and the locations haven't changed, use it
      if (cache.has(regionGroup) && cache.get(regionGroup).locations === assistedLocations) {
        return cache.get(regionGroup).result;
      }

      // Otherwise compute the result
      if (!assistedLocations[regionGroup]) {
        const result = false;
        cache.set(regionGroup, { result, locations: assistedLocations });
        return result;
      }

      const regions = Object.keys(regionData[regionGroup]);
      const result = regions.every(region => isRegionFullySelected(regionGroup, region));

      // Cache the result
      cache.set(regionGroup, { result, locations: assistedLocations });
      return result;
    };
  })();

  // Memoized function to check if any region in a region group is selected
  const isRegionGroupPartiallySelected = (() => {
    const cache = new Map();

    return (regionGroup) => {
      // If we have a cached result and the locations haven't changed, use it
      if (cache.has(regionGroup) && cache.get(regionGroup).locations === assistedLocations) {
        return cache.get(regionGroup).result;
      }

      // Otherwise compute the result
      if (!assistedLocations[regionGroup]) {
        const result = false;
        cache.set(regionGroup, { result, locations: assistedLocations });
        return result;
      }

      const regions = Object.keys(regionData[regionGroup]);
      let fullySelectedCount = 0;
      let partiallySelectedCount = 0;

      // More efficient counting without creating intermediate arrays
      for (const region of regions) {
        if (isRegionFullySelected(regionGroup, region)) {
          fullySelectedCount++;
        } else if (isRegionPartiallySelected(regionGroup, region)) {
          partiallySelectedCount++;
        }
      }

      const result = (fullySelectedCount > 0 || partiallySelectedCount > 0) &&
                    (fullySelectedCount < regions.length || partiallySelectedCount > 0);

      // Cache the result
      cache.set(regionGroup, { result, locations: assistedLocations });
      return result;
    };
  })();

  // Get a list of all assisted provinces - memoized for better performance
  const getAssistedProvincesList = (() => {
    let cachedResult = [];
    let lastLocations = null;

    return () => {
      // If assistedLocations hasn't changed, return the cached result
      if (lastLocations === assistedLocations) {
        return cachedResult;
      }

      // Otherwise, compute the new list
      const assistedProvinces = [];

      // Use a more efficient approach with for...of loops
      for (const regionGroup in assistedLocations) {
        const regions = assistedLocations[regionGroup] || {};

        for (const region in regions) {
          const provinces = regions[region] || {};

          for (const province in provinces) {
            if (provinces[province]) {
              assistedProvinces.push(province);
            }
          }
        }
      }

      // Cache the result and the reference to assistedLocations
      cachedResult = assistedProvinces;
      lastLocations = assistedLocations;

      return assistedProvinces;
    };
  })();

  if (loading) {
    return <div className="loading">Loading locations data...</div>;
  }

  return (
    <div className="assisted-locations">
      <div className="locations-header">
        <h2>Assisted Delivery Locations</h2>
        <p>Select the provinces where Pixel Prints provides delivery services.</p>
      </div>

      {saveStatus && <div className="save-status success">{saveStatus}</div>}

      <div className="locations-container">
        {Object.keys(regionData).map(regionGroup => (
          <div key={regionGroup} className="region-group">
            <div className="region-group-header" onClick={() => toggleExpand(regionGroup)}>
              <IndeterminateCheckbox
                id={`group-${regionGroup}`}
                checked={isRegionGroupFullySelected(regionGroup)}
                indeterminate={isRegionGroupPartiallySelected(regionGroup)}
                onChange={() => toggleRegionGroup(regionGroup)}
                label={regionGroup}
              />
              <span
                className="expand-icon"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpand(regionGroup);
                }}
              >
                {expandedRegions[regionGroup] ? '▼' : '►'}
              </span>
            </div>

            {expandedRegions[regionGroup] && (
              <div className="regions">
                {Object.keys(regionData[regionGroup]).map(region => (
                  <div key={region} className="region">
                    <div className="region-header">
                      <IndeterminateCheckbox
                        id={`region-${region}`}
                        checked={isRegionFullySelected(regionGroup, region)}
                        indeterminate={isRegionPartiallySelected(regionGroup, region)}
                        onChange={() => toggleRegion(regionGroup, region)}
                        label={region}
                      />
                    </div>

                    <div className="provinces">
                      {regionData[regionGroup][region].map(province => (
                        <div key={province} className="province">
                          <IndeterminateCheckbox
                            id={`province-${province}`}
                            checked={assistedLocations[regionGroup]?.[region]?.[province] || false}
                            indeterminate={false}
                            onChange={() => toggleProvince(regionGroup, region, province)}
                            label={province}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="assisted-summary">
        <h3>Currently Assisted Provinces</h3>
        <div className="assisted-provinces-list">
          {getAssistedProvincesList().length > 0 ? (
            getAssistedProvincesList().sort().map(province => (
              <span key={province} className="assisted-province">{province}</span>
            ))
          ) : (
            <p>No provinces selected yet.</p>
          )}
        </div>
      </div>

      <div className="actions">
        <button className="save-button" onClick={saveLocations}>
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default AssistedLocations;
