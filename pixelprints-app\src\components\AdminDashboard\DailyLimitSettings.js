import React, { useState, useEffect } from 'react';

const DailyLimitSettings = ({ settings, updateDailyOrderLimit, ordersToday }) => {
  const [limit, setLimit] = useState(10);
  const [enabled, setEnabled] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');

  // Initialize form with current settings
  useEffect(() => {
    if (settings.daily_order_limit) {
      setLimit(parseInt(settings.daily_order_limit, 10));
    }
    
    if (settings.daily_order_limit_enabled !== undefined) {
      setEnabled(settings.daily_order_limit_enabled === 'true');
    }
  }, [settings]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage('');
    
    try {
      await updateDailyOrderLimit(limit, enabled);
      setMessage('Settings updated successfully!');
    } catch (error) {
      setMessage('Failed to update settings. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if limit is reached
  const isLimitReached = enabled && ordersToday >= limit;

  return (
    <div className="daily-limit-settings">
      <h2>Daily Order Limit Settings</h2>
      
      <form className="settings-form" onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="daily-limit">Maximum Orders Per Day:</label>
          <input
            id="daily-limit"
            type="number"
            min="1"
            value={limit}
            onChange={(e) => setLimit(parseInt(e.target.value, 10))}
            required
          />
        </div>
        
        <div className="form-group">
          <div className="checkbox-container">
            <input
              id="limit-enabled"
              type="checkbox"
              checked={enabled}
              onChange={(e) => setEnabled(e.target.checked)}
            />
            <label htmlFor="limit-enabled">Enable Daily Order Limit</label>
          </div>
        </div>
        
        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Updating...' : 'Save Settings'}
        </button>
        
        {message && <p className="message">{message}</p>}
      </form>
      
      <div className="current-status">
        <h3>Current Status</h3>
        <p><strong>Limit Enabled:</strong> {enabled ? 'Yes' : 'No'}</p>
        <p><strong>Maximum Orders Per Day:</strong> {limit}</p>
        <p><strong>Orders Today:</strong> {ordersToday}</p>
        <p>
          <strong>Status:</strong> 
          {isLimitReached ? (
            <span className="limit-reached">Limit Reached</span>
          ) : (
            <span className="limit-not-reached">
              {enabled ? `${ordersToday} of ${limit} orders used` : 'No limit applied'}
            </span>
          )}
        </p>
      </div>
    </div>
  );
};

export default DailyLimitSettings;
