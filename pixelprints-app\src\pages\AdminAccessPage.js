import React from 'react';
import Logo from '../components/Logo';

function AdminAccessPage({ setCurrentView }) {
  return (
    <div className="admin-access-page">
      <header className="App-header">
        <div className="header-content">
          <Logo />
          <div className="slogan">Knock, knock! Who's there? Your Prints</div>
          
          <div className="admin-access-container">
            <h2>Admin Access</h2>
            <p>This page is for administrators only. Please log in to access the admin dashboard.</p>
            <button 
              onClick={() => setCurrentView('adminLogin')} 
              className="admin-login-button"
            >
              Admin Login
            </button>
            <div className="back-link">
              <button 
                onClick={() => setCurrentView('home')} 
                className="back-to-home-button"
              >
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </header>
    </div>
  );
}

export default AdminAccessPage;
