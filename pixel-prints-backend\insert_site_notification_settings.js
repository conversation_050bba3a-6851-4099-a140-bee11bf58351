const db = require('./db');

async function insertSiteNotificationSettings() {
  try {
    // Check if settings already exist
    const checkResult = await db.query(
      'SELECT * FROM settings WHERE key = $1 OR key = $2',
      ['site-notification-message', 'site-notification-enabled']
    );

    if (checkResult.rows.length === 0) {
      // Add site notification settings
      await db.query(`
        INSERT INTO settings (key, value, description)
        VALUES
          ('site-notification-message', 'The website is currently undergoing maintenance. Some features may be unavailable.', 'Message to display as site notification'),
          ('site-notification-enabled', 'false', 'Whether the site notification is enabled');
      `);
      console.log('Site notification settings added successfully');
    } else {
      console.log('Site notification settings already exist');
    }

  } catch (error) {
    console.error('Error adding site notification settings:', error);
  }
}

insertSiteNotificationSettings();
