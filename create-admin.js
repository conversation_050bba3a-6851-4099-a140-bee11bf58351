const bcrypt = require('bcryptjs');
const db = require('./pixel-prints-backend/db');
require('dotenv').config();

async function createAdmin() {
  try {
    console.log('Creating admin user...');
    
    // Admin credentials from .env or defaults
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const adminName = 'Admin User';
    
    console.log(`Admin email: ${adminEmail}`);
    
    // Check if admin already exists
    const existingAdmin = await db.query('SELECT * FROM users WHERE email = $1', [adminEmail]);
    
    if (existingAdmin.rows.length > 0) {
      console.log('Admin user already exists. Updating password...');
      
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(adminPassword, salt);
      
      // Update admin password
      await db.query(
        'UPDATE users SET password = $1 WHERE email = $2',
        [hashedPassword, adminEmail]
      );
      
      console.log('Admin password updated successfully');
    } else {
      console.log('Creating new admin user...');
      
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(adminPassword, salt);
      
      // Create admin user
      const result = await db.query(
        'INSERT INTO users (name, email, password, role) VALUES ($1, $2, $3, $4) RETURNING id, name, email, role',
        [adminName, adminEmail, hashedPassword, 'admin']
      );
      
      console.log('Admin user created successfully:', result.rows[0]);
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    // Close the database connection
    await db.end();
    process.exit(0);
  }
}

// Run the function
createAdmin();
