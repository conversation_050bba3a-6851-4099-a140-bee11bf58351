const express = require('express');
const router = express.Router();
const { authenticateToken, isAdmin } = require('../middleware/auth');
const { 
  getAllOrders, 
  getOrderById,
  updateOrderStatus,
  deleteOrder,
  getOrderStats
} = require('../controllers/orders');
const {
  getAllUsers,
  getUserById,
  updateUserRole,
  deleteUser
} = require('../controllers/users');

// Order routes
router.get('/orders', authenticateToken, isAdmin, getAllOrders);
router.get('/orders/:id', authenticateToken, isAdmin, getOrderById);
router.put('/orders/:id/status', authenticateToken, isAdmin, updateOrderStatus);
router.delete('/orders/:id', authenticateToken, isAdmin, deleteOrder);
router.get('/order-stats', authenticateToken, isAdmin, getOrderStats);

// User routes
router.get('/users', authenticateToken, isAdmin, getAllUsers);
router.get('/users/:id', authenticateToken, isAdmin, getUserById);
router.put('/users/:id/role', authenticateToken, isAdmin, updateUserRole);
router.delete('/users/:id', authenticateToken, isAdmin, deleteUser);

module.exports = router;