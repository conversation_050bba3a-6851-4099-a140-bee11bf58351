.admin-login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 20px;
}

.admin-login-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
  width: 100%;
  max-width: 500px;
}

.admin-login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.admin-login-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.back-button {
  background-color: #f0f0f0;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #555;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  border-color: #1976d2;
  outline: none;
}

.admin-login-button {
  background-color: #2e7d32;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.admin-login-button:hover {
  background-color: #1b5e20;
}

.admin-login-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

.forgot-password {
  text-align: right;
  margin-bottom: 15px;
}

.forgot-password-link {
  background: none;
  border: none;
  color: #2e7d32;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
}

.forgot-password-link:hover {
  color: #1b5e20;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

.admin-login-footer {
  margin-top: 20px;
  text-align: center;
}

.switch-button {
  background: none;
  border: none;
  color: #1976d2;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
}

.switch-button:hover {
  color: #0d47a1;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .admin-login-page {
    padding: 15px;
  }

  .admin-login-container {
    padding: 25px;
  }

  .admin-login-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .admin-login-header h1 {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  .admin-login-page {
    padding: 10px;
    min-height: 70vh;
  }

  .admin-login-container {
    padding: 20px;
  }

  .admin-login-header h1 {
    font-size: 20px;
  }

  .form-group label {
    font-size: 14px;
  }

  .form-group input {
    padding: 10px;
    font-size: 14px;
  }

  .admin-login-button {
    padding: 10px;
    font-size: 14px;
  }

  .back-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .error-message {
    padding: 10px;
    font-size: 14px;
  }
}
