/**
 * <PERSON><PERSON><PERSON> to recreate the admin user
 */

const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
require('dotenv').config();

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function recreateAdmin() {
  try {
    console.log('Checking if users table exists...');
    
    // Check if users table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'users'
      );
    `);
    
    // If users table doesn't exist, create it
    if (!tableCheck.rows[0].exists) {
      console.log('Creating users table...');
      await pool.query(`
        CREATE TABLE users (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          email VARCHAR(100) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          role VARCHAR(20) DEFAULT 'user',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          reset_token VARCHAR(255),
          reset_token_expires TIMESTAMP
        );
      `);
      console.log('Users table created successfully.');
    }
    
    // Check if admin user already exists
    console.log('Checking if admin user exists...');
    const adminCheck = await pool.query(`
      SELECT * FROM users WHERE email = '<EMAIL>';
    `);
    
    // If admin user exists, delete it
    if (adminCheck.rows.length > 0) {
      console.log('Admin user already exists. Deleting...');
      await pool.query(`
        DELETE FROM users WHERE email = '<EMAIL>';
      `);
      console.log('Admin user deleted successfully.');
    }
    
    // Create admin user
    console.log('Creating admin user...');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    await pool.query(`
      INSERT INTO users (name, email, password, role)
      VALUES ('Admin', '<EMAIL>', $1, 'admin');
    `, [hashedPassword]);
    
    console.log('Admin user created successfully.');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
    // Close the database connection
    await pool.end();
    
    console.log('Done!');
  } catch (error) {
    console.error('Error recreating admin user:', error);
    process.exit(1);
  }
}

// Run the function
recreateAdmin();
