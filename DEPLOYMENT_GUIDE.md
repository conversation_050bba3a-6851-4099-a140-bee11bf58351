# Pixel Prints Deployment Guide

This guide explains how to deploy the Pixel Prints application to Railway (backend) and Netlify (frontend).

## Prerequisites

- [Node.js](https://nodejs.org/) installed
- [Git](https://git-scm.com/) installed
- [Railway CLI](https://docs.railway.app/develop/cli) installed
- [Netlify CLI](https://docs.netlify.com/cli/get-started/) installed
- A Railway account
- A Netlify account
- Git repository with your Pixel Prints code

## Deployment Options

### Option 1: Automated Deployment (Recommended)

We've provided a comprehensive deployment script that handles the entire process:

#### PowerShell (Windows)

```powershell
.\deploy-full-stack.ps1
```

#### Bash (Linux/Mac)

```bash
chmod +x deploy-full-stack.sh
./deploy-full-stack.sh
```

The script will:
1. Install Railway and Netlify CLIs if needed
2. Login to Railway and Netlify
3. Push your code to GitHub (optional)
4. Deploy the backend to Railway
5. Set up a PostgreSQL database
6. Configure environment variables
7. Initialize the database
8. Update the frontend configuration
9. Deploy the frontend to Netlify

### Option 2: Manual Deployment

If you prefer to deploy manually, follow these steps:

#### Backend Deployment (Railway)

1. **Install Railway CLI**

```bash
npm install -g @railway/cli
```

2. **Login to Railway**

```bash
railway login
```

3. **Create a New Project**

```bash
railway init --name pixel-prints-backend
```

4. **Add PostgreSQL Database**

```bash
railway add --plugin postgresql
```

5. **Navigate to Backend Directory**

```bash
cd pixel-prints-backend
```

6. **Set Environment Variables**

```bash
railway vars set NODE_ENV=production
railway vars set FRONTEND_URL=https://your-netlify-url.netlify.app
railway vars set JWT_SECRET=pixel-prints-secure-jwt-key-2024
railway vars set ADMIN_EMAIL=<EMAIL>
railway vars set ADMIN_PASSWORD=admin123
railway vars set EMAIL_USER=<EMAIL>
railway vars set EMAIL_PASS=your-email-password
```

7. **Link the Directory to the Railway Project**

```bash
railway link
```

8. **Deploy the Backend**

```bash
railway up
```

9. **Initialize the Database**

```bash
railway run node db/init.js
railway run node db/setup_settings.js
railway run node db/create_admin.js
railway run node db/add_site_notification_settings.js
```

10. **Get the Deployed URL**

```bash
railway status --json
```

#### Frontend Deployment (Netlify)

1. **Install Netlify CLI**

```bash
npm install -g netlify-cli
```

2. **Login to Netlify**

```bash
netlify login
```

3. **Update Frontend Configuration**

Update `pixelprints-app/.env.production`:

```
REACT_APP_API_URL=https://your-railway-url.up.railway.app
```

Update `pixelprints-app/netlify.toml` to include your Railway domain in the Content-Security-Policy.

4. **Navigate to Frontend Directory**

```bash
cd pixelprints-app
```

5. **Build the Frontend**

```bash
npm install
npm run build
```

6. **Deploy to Netlify**

```bash
netlify deploy --prod
```

## CORS Configuration

CORS (Cross-Origin Resource Sharing) is a critical aspect of the deployment. If you encounter CORS issues, use our CORS fixer script:

#### PowerShell (Windows)

```powershell
.\fix-cors.ps1 -BackendUrl "https://your-railway-url.up.railway.app" -FrontendUrl "https://your-netlify-url.netlify.app"
```

#### Bash (Linux/Mac)

```bash
chmod +x fix-cors.sh
./fix-cors.sh "https://your-railway-url.up.railway.app" "https://your-netlify-url.netlify.app"
```

The script will:
1. Update the backend CORS configuration
2. Update the frontend Content Security Policy
3. Create a test file to verify CORS is working

### Manual CORS Fixes

If you need to fix CORS issues manually:

1. **Update Railway Environment Variables**

Make sure the `FRONTEND_URL` environment variable in Railway matches your Netlify URL:

```bash
railway vars set FRONTEND_URL=https://your-netlify-url.netlify.app
```

2. **Update Content Security Policy**

Edit `pixelprints-app/netlify.toml` and ensure your Railway domain is included in the Content-Security-Policy:

```toml
Content-Security-Policy = "default-src 'self'; connect-src 'self' https://*.railway.app https://*.up.railway.app https://your-railway-url.up.railway.app; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval';"
```

3. **Redeploy Frontend**

After making changes to the Content Security Policy, redeploy your frontend:

```bash
netlify deploy --prod
```

## Testing the Deployment

After deployment, test your application to ensure everything is working correctly:

1. **Test the Backend**

```
https://your-railway-url.up.railway.app/api/test
```

2. **Test CORS**

```
https://your-railway-url.up.railway.app/api/cors-test
```

3. **Test the Frontend**

Visit your Netlify URL and try to:
- Log in as admin
- Submit an order
- View orders

## Troubleshooting

### Database Connection Issues

If you have issues connecting to the database:

1. Verify that the `DATABASE_URL` environment variable is set correctly
2. Check the Railway logs for any database connection errors
3. Make sure your IP is allowed to connect to the database

### CORS Issues

If you encounter CORS issues:

1. Verify that the `FRONTEND_URL` environment variable in Railway matches your Netlify URL
2. Check the CORS configuration in server.js
3. Update the Content Security Policy in your frontend's netlify.toml file
4. Use the CORS test endpoint to diagnose issues: `/api/cors-test`

### Deployment Issues

If you encounter issues during deployment:

1. Check the Railway and Netlify logs for any errors
2. Verify that all environment variables are set correctly
3. Make sure your project is linked to the correct Railway project

## Additional Resources

- [Railway Documentation](https://docs.railway.app/)
- [Railway CLI Documentation](https://docs.railway.app/develop/cli)
- [Netlify Documentation](https://docs.netlify.com/)
- [Netlify CLI Documentation](https://docs.netlify.com/cli/get-started/)
