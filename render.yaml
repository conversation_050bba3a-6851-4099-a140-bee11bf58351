services:
  - type: web
    name: pixel-prints-backend
    env: docker
    rootDir: pixel-prints-backend
    dockerfilePath: ./Dockerfile
    dockerContext: .
    plan: free
    branch: main
    numInstances: 1
    healthCheckPath: /api/test
    envVars:
      - key: NODE_ENV
        value: production
      - key: FRONTEND_URL
        value: https://jocular-jelly-4e9cef.netlify.app
      - key: JWT_SECRET
        value: pixel-prints-secure-jwt-key-2024
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: ADMIN_PASSWORD
        value: admin123
      - key: EMAIL_USER
        value: <EMAIL>
      - key: EMAIL_PASS
        value: rltvbslrmehnrtyj
      - key: DATABASE_URL
        fromDatabase:
          name: pixel-prints-db
          property: connectionString

databases:
  - name: pixel-prints-db
    plan: free
    databaseName: pixel_prints
    user: pixel_prints_user
