const db = require('./db');

async function runMigrations() {
  try {
    console.log('Running database migrations...');
    
    // Check if comments column exists in orders table
    const checkResult = await db.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'orders' AND column_name = 'comments'
    `);

    if (checkResult.rows.length === 0) {
      console.log('Adding comments column to orders table...');
      
      // Add comments column as JSONB with default empty array
      await db.query(`
        ALTER TABLE orders 
        ADD COLUMN comments JSONB DEFAULT '[]'::jsonb
      `);
      
      console.log('Comments column added successfully');
    } else {
      console.log('Comments column already exists in orders table');
    }
    
    console.log('Migrations completed successfully');
  } catch (error) {
    console.error('Error running migrations:', error);
  } finally {
    // Close the database connection
    await db.end();
  }
}

// Run the migrations
runMigrations();
