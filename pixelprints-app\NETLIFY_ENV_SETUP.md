# Setting Up Environment Variables in Netlify

This guide explains how to set up environment variables for your Pixel Prints frontend application deployed on Netlify.

## Environment Variables

The following environment variables need to be set in your Netlify deployment:

| Variable | Description | Example Value |
|----------|-------------|---------------|
| `REACT_APP_API_URL` | URL of your backend API | `https://pixel-prints-1.onrender.com` |
| `REACT_APP_VERSION` | Application version | `1.0.0` |
| `REACT_APP_ENABLE_ANALYTICS` | Enable/disable analytics | `true` |
| `REACT_APP_ENABLE_NOTIFICATIONS` | Enable/disable notifications | `true` |
| `REACT_APP_APP_NAME` | Application name | `Pixel Prints` |
| `REACT_APP_COMPANY_NAME` | Company name | `Pixel Prints` |
| `REACT_APP_CONTACT_EMAIL` | Contact email | `<EMAIL>` |
| `REACT_APP_CONTACT_PHONE` | Contact phone | `+************` |

## Setting Up in Netlify UI

1. Log in to your Netlify account
2. Go to your site dashboard
3. Click on "Site settings"
4. In the left sidebar, click on "Environment variables"
5. Click "Add variable" and add each of the variables listed above
6. After adding all variables, trigger a new deployment by clicking "Trigger deploy" in the "Deploys" tab

## Setting Up via netlify.toml

You can also set environment variables in your `netlify.toml` file:

```toml
[build.environment]
  REACT_APP_API_URL = "https://pixel-prints-1.onrender.com"
  REACT_APP_VERSION = "1.0.0"
  REACT_APP_ENABLE_ANALYTICS = "true"
  REACT_APP_ENABLE_NOTIFICATIONS = "true"
  REACT_APP_APP_NAME = "Pixel Prints"
  REACT_APP_COMPANY_NAME = "Pixel Prints"
  REACT_APP_CONTACT_EMAIL = "<EMAIL>"
  REACT_APP_CONTACT_PHONE = "+************"
```

## Important Notes

1. Environment variables in Netlify are applied at build time, not runtime. This means you need to trigger a new deployment after changing environment variables.

2. All environment variables used in React must start with `REACT_APP_` to be accessible in your application.

3. To access these variables in your React code, use: `process.env.REACT_APP_VARIABLE_NAME`

4. For local development, the values in `.env.development` will be used.

5. For production builds, the values in `.env.production` will be used, but can be overridden by the values set in Netlify.
