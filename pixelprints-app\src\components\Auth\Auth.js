"use client"

import { useState, useRef, useEffect, memo } from "react"
import { useNavigate } from "react-router-dom"
import "./Auth.css"
import { getApiUrl, fetchApi } from "../../utils/api"
import logger from "../../utils/logger"
import { mockLogin, mockSignup } from "../../utils/mockAuthService"

// Using memo to prevent unnecessary re-renders from parent component
const Auth = memo(({ onAuthSuccess }) => {
  const navigate = useNavigate()

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: ""
  })

  // UI state
  const [isLogin, setIsLogin] = useState(true)
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // Refs for form elements
  const nameInputRef = useRef(null)
  const emailInputRef = useRef(null)
  const passwordInputRef = useRef(null)
  const formRef = useRef(null)

  // Animation state
  const [formAnimation, setFormAnimation] = useState("")

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Focus appropriate field when switching between login/signup
  useEffect(() => {
    if (!isLogin && nameInputRef.current) {
      nameInputRef.current.focus()
    } else if (isLogin && emailInputRef.current) {
      emailInputRef.current.focus()
    }
  }, [isLogin])

  // Initialize form state when component mounts
  useEffect(() => {
    // Reset form data
    setFormData({
      name: "",
      email: "",
      password: ""
    })

    // Default to login mode
    setIsLogin(true)

    // Clear any errors
    setError("")
  }, [])

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault()
    setError("")

    const { name, email, password } = formData

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setError("Please enter a valid email address.")
      return
    }

    // Validate name for registration
    if (!isLogin && !name) {
      setError("Name is required for registration.")
      return
    }

    setIsLoading(true)

    try {
      // Determine if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';
      let data;

      if (isDevelopment) {
        // Use mock service in development mode
        logger.info('Using mock authentication service in development mode');
        data = isLogin
          ? await mockLogin(email, password)
          : await mockSignup(name, email, password);
      } else {
        // In production, use the real API
        const endpoint = isLogin ? "/api/login" : "/api/signup"
        const payload = isLogin ? { email, password } : { email, password, name }

        // Make API request using fetchApi utility
        const response = await fetch(getApiUrl(endpoint), {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        })

        data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || "Authentication failed")
        }
      }

      // Store user data
      localStorage.setItem("token", data.token)
      localStorage.setItem("userName", data.user.name)
      localStorage.setItem("userEmail", data.user.email)
      localStorage.setItem("userRole", data.user.role || "user")
      localStorage.setItem("isAuthenticated", "true")

      // Call success callback
      onAuthSuccess(data.user)
    } catch (error) {
      logger.error("Auth error:", error)
      setError(error.message || "An error occurred during authentication")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle switching between login and signup
  const handleModeSwitch = () => {
    // Add animation class
    setFormAnimation("form-fade-out")

    // After animation completes, switch mode and reset animation
    setTimeout(() => {
      // Toggle the login mode
      setIsLogin(prevMode => !prevMode)

      // Clear any errors
      setError("")

      // Add fade-in animation
      setFormAnimation("form-fade-in")

      // Reset password field but keep email for convenience
      setFormData(prev => ({
        ...prev,
        name: "",  // Always reset name field
        password: "" // Always reset password field
      }))

      // Remove animation class after animation completes
      setTimeout(() => {
        setFormAnimation("")
      }, 300)
    }, 200)
  }

  return (
    <div className="auth-page">
      <div className={`auth-container ${formAnimation}`}>
        <div className="auth-header">
          <h1>{isLogin ? "Welcome Back" : "Create Account"}</h1>
          <p>{isLogin ? "Sign in to continue" : "Register to get started"}</p>
        </div>

        {error && <div className="error-message">{error}</div>}

        <form ref={formRef} onSubmit={handleSubmit} className="auth-form">
          {!isLogin && (
            <div className="form-group">
              <label htmlFor="name">Full Name</label>
              <input
                type="text"
                id="name"
                name="name"
                ref={nameInputRef}
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter your full name"
                required={!isLogin}
              />
            </div>
          )}

          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              ref={emailInputRef}
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              ref={passwordInputRef}
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Enter your password"
              required
            />
          </div>

          {isLogin && (
            <div className="forgot-password">
              <button
                type="button"
                className="forgot-password-link"
                onClick={() => navigate('/forgot-password')}
              >
                Forgot Password?
              </button>
            </div>
          )}

          <button type="submit" className="auth-button" disabled={isLoading}>
            {isLoading ? "Please wait..." : (isLogin ? "Sign In" : "Create Account")}
          </button>
        </form>

        <div className="auth-footer">
          {isLogin ? "Don't have an account?" : "Already have an account?"}
          <button
            type="button"
            className="switch-button"
            onClick={handleModeSwitch}
          >
            {isLogin ? "Sign Up" : "Sign In"}
          </button>
        </div>
      </div>
    </div>
  )
});

export default Auth
