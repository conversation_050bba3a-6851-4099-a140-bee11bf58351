<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="256" cy="256" r="256" fill="#ffffff"/>
  
  <!-- Printer design based on the provided image -->
  <g transform="translate(106, 106) scale(3)">
    <!-- Printer top (gray) -->
    <rect x="10" y="10" width="80" height="15" fill="#c0c0c0" rx="2" />
    
    <!-- Printer body (blue) -->
    <rect x="10" y="25" width="80" height="40" fill="#4a90e2" rx="3" />
    
    <!-- Printer output tray (dark gray) -->
    <rect x="20" y="65" width="60" height="10" fill="#333333" rx="1" />
    
    <!-- Printer button (white) -->
    <circle cx="80" cy="35" r="3" fill="#ffffff" />
  </g>
  
  <!-- Text for Pixel Prints -->
  <text x="256" y="380" text-anchor="middle" font-family="Arial, sans-serif" 
        font-weight="bold" font-size="40" fill="#333333">PIXEL PRINTS</text>
</svg>
