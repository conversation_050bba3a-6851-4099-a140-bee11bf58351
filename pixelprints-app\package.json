{"name": "pixelprints-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.3", "date-fns": "^4.1.0", "html-to-image": "^1.11.13", "konva": "^9.3.20", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-konva": "^18.2.10", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "use-image": "^1.1.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}