# Deployment Guide for PixelPrints App

This guide provides instructions for deploying the PixelPrints application to a production environment.

## Prerequisites

- Node.js (v14 or higher)
- PostgreSQL database
- Domain name (for production)
- Email service credentials

## Backend Deployment

### 1. Set up environment variables

Create a `.env` file in the `pixel-prints-backend` directory with the following variables:

```
# Email configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Server configuration
PORT=3001

# Frontend URL (important for password reset links)
FRONTEND_URL=https://your-domain.com

# Database configuration
DATABASE_URL=postgres://username:password@localhost:5432/pixelprints

# JWT Secret (use a strong random string)
JWT_SECRET=your-secure-jwt-secret
```

Replace the placeholder values with your actual production values.

### 2. Install dependencies and build

```bash
cd pixel-prints-backend
npm install --production
```

### 3. Set up the database

Run the database initialization scripts:

```bash
npm run init-db
npm run update-users
npm run create-admin
```

### 4. Start the server

For production, it's recommended to use a process manager like PM2:

```bash
npm install -g pm2
pm2 start server.js --name pixel-prints-backend
```

## Frontend Deployment

### 1. Set up environment variables

Create a `.env` file in the `pixelprints-app` directory:

```
REACT_APP_API_URL=https://api.your-domain.com
```

### 2. Build the frontend

```bash
cd pixelprints-app
npm install
npm run build
```

This will create a `build` directory with optimized production files.

### 3. Deploy the build files

Upload the contents of the `build` directory to your web server or hosting service.

## Domain and SSL Setup

1. Configure your domain to point to your server
2. Set up SSL certificates for both frontend and backend domains
3. Configure your web server (Nginx, Apache, etc.) to serve the frontend files and proxy API requests to the backend

## Testing the Deployment

1. Visit your domain in a browser
2. Test the authentication flow, including the forgot password feature
3. Verify that password reset emails contain the correct domain in the reset link

## Troubleshooting

- If password reset links don't work, check that the `FRONTEND_URL` environment variable is set correctly
- If emails aren't being sent, verify your email service credentials
- Check server logs for any errors

## Maintenance

- Regularly update dependencies
- Monitor server performance
- Back up your database regularly
