const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3002;

// Target API URL
const TARGET_API_URL = 'https://pixel-prints-production.up.railway.app';

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Health check endpoint
app.get('/', (req, res) => {
  res.json({ message: 'Pixel Prints Proxy Server is running!' });
});

// Proxy all requests
app.all('*', async (req, res) => {
  try {
    const targetUrl = `${TARGET_API_URL}${req.url}`;
    console.log(`Proxying request to: ${targetUrl}`);

    // Forward the request to the target API
    const response = await axios({
      method: req.method,
      url: targetUrl,
      data: req.method !== 'GET' ? req.body : undefined,
      headers: {
        ...req.headers,
        host: new URL(TARGET_API_URL).host
      },
      responseType: 'stream'
    });

    // Forward the response back to the client
    response.data.pipe(res);
  } catch (error) {
    console.error('Proxy error:', error.message);
    
    // Forward error response if available
    if (error.response) {
      res.status(error.response.status).json({
        error: 'Proxy error',
        message: error.message,
        status: error.response.status
      });
    } else {
      res.status(500).json({
        error: 'Proxy error',
        message: error.message
      });
    }
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Proxy server is running on port ${PORT}`);
});
