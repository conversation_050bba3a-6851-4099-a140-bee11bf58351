const bcrypt = require('bcryptjs');
const db = require('../db');

async function createCorrectAdmin() {
  try {
    console.log('Creating admin user <NAME_EMAIL>...');
    
    // <NAME_EMAIL> exists
    const adminCheck = await db.query(`
      SELECT * FROM users WHERE email = '<EMAIL>';
    `);
    
    if (adminCheck.rows.length > 0) {
      console.log('Admin user <NAME_EMAIL> already exists. Updating password...');
      
      // Update password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      await db.query(`
        UPDATE users 
        SET password = $1
        WHERE email = '<EMAIL>';
      `, [hashedPassword]);
      
      console.log('Admin password updated successfully.');
    } else {
      console.log('Admin user <NAME_EMAIL> does not exist. Creating it...');
      
      // Create admin user with password 'admin123'
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      await db.query(`
        INSERT INTO users (name, email, password, role)
        VALUES ('Admin', '<EMAIL>', $1, 'admin');
      `, [hashedPassword]);
      
      console.log('Admin user created successfully.');
    }
    
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
  } catch (error) {
    console.error('Error creating/updating admin user:', error);
  }
}

createCorrectAdmin()
  .then(() => {
    console.log('Script completed.');
    process.exit(0);
  })
  .catch(err => {
    console.error('Script failed:', err);
    process.exit(1);
  });
