.admin-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  overflow-x: hidden;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.admin-header h1 {
  margin: 0;
  color: #333;
}

.back-button {
  background-color: #f0f0f0;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

.admin-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s;
}

.tab-button:hover {
  color: #333;
}

.tab-button.active {
  color: #1976d2;
  border-bottom: 2px solid #1976d2;
}

.tab-content {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow-x: auto;
}

/* Stats container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #555;
  font-size: 16px;
}

.stat-card .stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1976d2;
  margin: 0;
}

/* Order list styles */
.order-list {
  width: 100%;
}

.order-list table {
  width: 100%;
  border-collapse: collapse;
}

.order-list th {
  text-align: left;
  padding: 12px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.order-list td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.order-list tr:hover {
  background-color: #f9f9f9;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.order-actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.view-button {
  background-color: #e3f2fd;
  color: #1976d2;
}

.complete-button {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.delete-button {
  background-color: #ffebee;
  color: #c62828;
}

.view-button:hover {
  background-color: #bbdefb;
}

.complete-button:hover {
  background-color: #c8e6c9;
}

.delete-button:hover {
  background-color: #ffcdd2;
}

/* Settings form */
.settings-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input[type="number"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group .checkbox-container {
  display: flex;
  align-items: center;
}

.form-group .checkbox-container input[type="checkbox"] {
  margin-right: 10px;
}

.settings-form button {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.settings-form button:hover {
  background-color: #1565c0;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 15px;
  }

  .admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .admin-header h1 {
    font-size: 1.5rem;
  }

  .admin-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }

  .tab-button {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .stats-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table {
    min-width: 600px;
  }

  .settings-form {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .admin-dashboard {
    padding: 10px;
  }

  .tab-content {
    padding: 15px;
  }

  .tab-button {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-card h3 {
    font-size: 1rem;
  }

  .stat-card p {
    font-size: 1.2rem;
  }

  .settings-form label {
    font-size: 0.9rem;
  }

  .settings-form input {
    padding: 8px;
    font-size: 0.9rem;
  }

  .settings-form button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

.current-status {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.current-status p {
  margin: 5px 0;
}

.limit-reached {
  color: #c62828;
  font-weight: bold;
}

.limit-not-reached {
  color: #2e7d32;
  font-weight: bold;
}

/* Site Notification Settings */
.site-notification-settings {
  max-width: 800px;
  margin: 0 auto;
}

.settings-description {
  margin-bottom: 20px;
  color: #666;
  line-height: 1.5;
}

.site-notification-settings textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
}

.notification-preview {
  margin-top: 30px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.site-notification-preview {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  margin-top: 10px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  border: 1px solid #f5c6cb;
}

.notification-disabled {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 10px;
}
