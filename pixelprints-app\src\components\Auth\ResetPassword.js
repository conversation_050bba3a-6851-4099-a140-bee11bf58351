import { useState, useEffect, useRef } from "react"
import { useNavigate, useParams } from "react-router-dom"
import "./Auth.css"
import { getApiUrl } from "../../utils/api"
import logger from "../../utils/logger"
import { mockResetPassword } from "../../utils/mockAuthService"

const ResetPassword = () => {
  const navigate = useNavigate()
  const { token } = useParams()

  // Form state
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: ""
  })

  // UI state
  const [message, setMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [formAnimation, setFormAnimation] = useState("")

  // Refs for form elements
  const passwordInputRef = useRef(null)
  const confirmPasswordInputRef = useRef(null)
  const formRef = useRef(null)

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Focus password input on component mount
  useEffect(() => {
    if (passwordInputRef.current) {
      passwordInputRef.current.focus()
    }
  }, [])

  useEffect(() => {
    // Validate token exists
    if (!token) {
      setMessage("Invalid password reset link.")
      return
    }
  }, [token])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setMessage("")
    setIsLoading(true)

    const { password, confirmPassword } = formData

    // Validate passwords match
    if (password !== confirmPassword) {
      setMessage("Passwords do not match.")
      setIsLoading(false)
      return
    }

    // Validate password length
    if (password.length < 6) {
      setMessage("Password must be at least 6 characters long.")
      setIsLoading(false)
      return
    }

    try {
      // Determine if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';
      let data;

      if (isDevelopment) {
        // Use mock service in development mode
        logger.info('Using mock reset password service in development mode');
        data = await mockResetPassword(token, password);
      } else {
        // In production, use the real API
        const response = await fetch(getApiUrl('/api/reset-password'), {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ token, password }),
        })

        data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || "An error occurred. Please try again.")
        }
      }

      // Add animation before showing success message
      setFormAnimation("form-fade-out")

      setTimeout(() => {
        setIsSuccess(true)
        setMessage(data.message || "Your password has been reset successfully.")
        setFormAnimation("form-fade-in")

        setTimeout(() => {
          setFormAnimation("")
        }, 300)
      }, 200)
    } catch (error) {
      logger.error("Reset password error:", error)
      setMessage("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle navigation with animation
  const handleNavigate = (path) => {
    setFormAnimation("form-fade-out")

    setTimeout(() => {
      navigate(path)
    }, 200)
  }

  return (
    <div className="auth-page">
      <div className={`auth-container ${formAnimation}`}>
        <div className="auth-header">
          <h1>Reset Password</h1>
          <p>Create a new password</p>
        </div>

        {isSuccess ? (
          <>
            <div className="success-message">
              <p>{message}</p>
            </div>
            <button
              type="button"
              className="auth-button"
              onClick={() => handleNavigate("/")}
            >
              Go to Home
            </button>
          </>
        ) : (
          <>
            <p>Enter your new password below.</p>

            {message && <div className={isSuccess ? "success-message" : "error-message"}>{message}</div>}

            <form ref={formRef} onSubmit={handleSubmit} className="auth-form">
              <div className="form-group">
                <label htmlFor="password">New Password</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  ref={passwordInputRef}
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  minLength="6"
                  placeholder="Enter new password"
                />
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirm Password</label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  ref={confirmPasswordInputRef}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                  minLength="6"
                  placeholder="Confirm new password"
                />
              </div>

              <button type="submit" className="auth-button" disabled={isLoading}>
                {isLoading ? "Resetting..." : "Reset Password"}
              </button>
            </form>

            <div className="auth-footer">
              <button
                type="button"
                className="switch-button"
                onClick={() => handleNavigate("/")}
              >
                Back to Home
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ResetPassword
