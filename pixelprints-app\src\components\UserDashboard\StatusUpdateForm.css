.status-update-form {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.status-update-form h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.status-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.status-option {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-option:hover {
  border-color: #aaa;
  background-color: #f9f9f9;
}

.status-option.selected {
  border-color: #4caf50;
  background-color: #f1f8e9;
}

.status-option-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-option-header label {
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
}

.status-description {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  padding-left: 1.5rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button,
.submit-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.submit-button {
  background-color: #4caf50;
  color: white;
}

.submit-button:hover {
  background-color: #45a049;
}

.cancel-button:disabled,
.submit-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.error-message {
  color: #e53935;
  background-color: rgba(229, 57, 53, 0.1);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

/* Status badge colors */
.status-option.selected[value="pending"] {
  border-color: #ff9800;
  background-color: #fff3e0;
}

.status-option.selected[value="processing"] {
  border-color: #2196f3;
  background-color: #e3f2fd;
}

.status-option.selected[value="completed"] {
  border-color: #4caf50;
  background-color: #e8f5e9;
}

.status-option.selected[value="delayed"] {
  border-color: #f44336;
  background-color: #ffebee;
}

.status-option.selected[value="cancelled"] {
  border-color: #9e9e9e;
  background-color: #f5f5f5;
}
