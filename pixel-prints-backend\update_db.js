cd const db = require('./db');

async function updateDatabase() {
    try {
        // Drop and recreate the orders table
        await db.query(`
            DROP TABLE IF EXISTS orders;
            
            CREATE TABLE orders (
                id SERIAL PRIMARY KEY,
                order_number VARCHAR(50) NOT NULL,
                customer_name VARCHAR(100) NOT NULL,
                customer_email VARCHAR(100) NOT NULL,
                print_type VARCHAR(50) NOT NULL,
                total_pages INTEGER,
                price DECIMAL(10,2) NOT NULL,
                address TEXT NOT NULL,
                province VARCHAR(100),
                delivery_charge DECIMAL(10,2) DEFAULT 0,
                files JSON,
                status VARCHAR(50) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        
        console.log('Database updated successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error updating database:', error);
        process.exit(1);
    }
}

updateDatabase();