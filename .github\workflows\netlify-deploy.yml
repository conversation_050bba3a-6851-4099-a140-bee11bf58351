name: Deploy to Netlify

on:
  push:
    branches:
      - main
      - frontend-api-connection-fix
    paths:
      - 'pixelprints-app/**'
      - '.github/workflows/netlify-deploy.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd pixelprints-app
          npm ci
          
      - name: Build
        run: |
          cd pixelprints-app
          npm run build
        env:
          REACT_APP_API_URL: ${{ secrets.HEROKU_APP_URL }}
          
      - name: Deploy to Netlify
        uses: nwtgck/actions-netlify@v1.2
        with:
          publish-dir: './pixelprints-app/build'
          production-branch: main
          github-token: ${{ secrets.GITHUB_TOKEN }}
          deploy-message: "Deploy from GitHub Actions"
          enable-pull-request-comment: true
          enable-commit-comment: true
          overwrites-pull-request-comment: true
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
        timeout-minutes: 5
