import { useState, useEffect, useRef } from 'react';
import { Stage, Layer, Rect, Text, Image, Transformer } from 'react-konva';
import Kon<PERSON> from 'konva';
import useImage from 'use-image';
import ImageUploader from './ImageUploader';
import './TShirtDesigner.css';

// T-shirt model component
const TShirtModel = ({ color, gender = 'male', view = 'front' }) => {
  // Use very simple t-shirt mockup images that are guaranteed to work
  const modelSrc = gender === 'male'
    ? (view === 'front'
      ? 'https://cdn.shopify.com/s/files/1/0568/6494/6945/files/white-t-shirt-front.png'
      : 'https://cdn.shopify.com/s/files/1/0568/6494/6945/files/white-t-shirt-back.png')
    : (view === 'front'
      ? 'https://cdn.shopify.com/s/files/1/0568/6494/6945/files/white-womens-t-shirt-front.png'
      : 'https://cdn.shopify.com/s/files/1/0568/6494/6945/files/white-womens-t-shirt-back.png');

  // For sleeve view, use a generic sleeve image
  const sleeveImage = 'https://cdn.shopify.com/s/files/1/0568/6494/6945/files/white-t-shirt-sleeve.png';

  // Use state to track image loading errors
  const [imageError, setImageError] = useState(false);

  // Handle image loading error
  const handleImageError = () => {
    console.error("Failed to load t-shirt image");
    setImageError(true);
  };

  return (
    <div className="tshirt-model-container">
      {imageError ? (
        <div className="tshirt-placeholder">
          <div className="tshirt-shape" style={{ backgroundColor: color }}>
            <span>{gender === 'male' ? 'Men\'s' : 'Women\'s'} T-Shirt</span>
            <span>{view} view</span>
          </div>
        </div>
      ) : (
        <div className="tshirt-image-container" style={{ position: 'relative' }}>
          <img
            src={view === 'sleeve' ? sleeveImage : modelSrc}
            alt={`${gender} model wearing a t-shirt, ${view} view`}
            onError={handleImageError}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              filter: color !== '#ffffff' ? `brightness(0.9) sepia(1) hue-rotate(${getHueRotation(color)}) saturate(${getSaturation(color)})` : 'none'
            }}
          />
          {/* Color overlay for non-white shirts */}
          {color !== '#ffffff' && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: color,
                opacity: 0.3,
                mixBlendMode: 'multiply',
                pointerEvents: 'none'
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};

// Helper functions for color transformations
const getHueRotation = (color) => {
  // Convert hex to RGB
  const r = parseInt(color.slice(1, 3), 16);
  const g = parseInt(color.slice(3, 5), 16);
  const b = parseInt(color.slice(5, 7), 16);

  // Calculate hue
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;

  if (max === min) {
    h = 0;
  } else if (max === r) {
    h = 60 * ((g - b) / (max - min));
  } else if (max === g) {
    h = 60 * (2 + (b - r) / (max - min));
  } else {
    h = 60 * (4 + (r - g) / (max - min));
  }

  if (h < 0) h += 360;

  // Return CSS hue-rotate value
  return `${h}deg`;
};

const getSaturation = (color) => {
  // Convert hex to RGB
  const r = parseInt(color.slice(1, 3), 16) / 255;
  const g = parseInt(color.slice(3, 5), 16) / 255;
  const b = parseInt(color.slice(5, 7), 16) / 255;

  // Calculate saturation
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const l = (max + min) / 2;

  if (max === min) {
    return 1; // grayscale
  } else {
    const s = l > 0.5 ? (max - min) / (2 - max - min) : (max - min) / (max + min);
    return Math.min(s * 5, 5); // Amplify saturation for better color effect
  }
};

// Text element component
const TextElement = ({
  x,
  y,
  text,
  fontSize,
  fontFamily,
  fill,
  rotation,
  isSelected,
  onSelect,
  onChange
}) => {
  const textRef = useRef();
  const trRef = useRef();

  // Update transformer on selection change
  useEffect(() => {
    if (isSelected && trRef.current && textRef.current) {
      // Attach transformer to the text
      trRef.current.nodes([textRef.current]);
      trRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  // Handle drag end
  const handleDragEnd = (e) => {
    onChange({
      x: e.target.x(),
      y: e.target.y()
    });
  };

  // Handle transform end
  const handleTransformEnd = () => {
    // Transformer changes scale, but we want to change fontSize instead
    const node = textRef.current;
    const scaleX = node.scaleX();

    // Reset scale and apply it to fontSize
    node.scaleX(1);
    node.scaleY(1);

    onChange({
      x: node.x(),
      y: node.y(),
      fontSize: Math.max(8, fontSize * scaleX),
      rotation: node.rotation()
    });
  };

  return (
    <>
      <Text
        ref={textRef}
        x={x}
        y={y}
        text={text}
        fontSize={fontSize}
        fontFamily={fontFamily}
        fill={fill}
        draggable
        rotation={rotation}
        onClick={onSelect}
        onTap={onSelect}
        onDragEnd={handleDragEnd}
        onTransformEnd={handleTransformEnd}
      />
      {isSelected && (
        <Transformer
          ref={trRef}
          enabledAnchors={['middle-left', 'middle-right']}
          boundBoxFunc={(oldBox, newBox) => {
            // Limit resize
            if (newBox.width < 5) {
              return oldBox;
            }
            return newBox;
          }}
        />
      )}
    </>
  );
};

// Image element component
const ImageElement = ({
  x,
  y,
  width,
  height,
  src,
  rotation,
  opacity = 1,
  blendMode = 'normal',
  filters = [],
  isSelected,
  onSelect,
  onChange
}) => {
  const imageRef = useRef();
  const trRef = useRef();
  const [image] = useImage(src);

  // Update transformer on selection change
  useEffect(() => {
    if (isSelected && trRef.current && imageRef.current) {
      // Attach transformer to the image
      trRef.current.nodes([imageRef.current]);
      trRef.current.getLayer().batchDraw();
    }
  }, [isSelected]);

  // Handle drag end
  const handleDragEnd = (e) => {
    onChange({
      x: e.target.x(),
      y: e.target.y()
    });
  };

  // Handle transform end
  const handleTransformEnd = () => {
    const node = imageRef.current;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();

    // Reset scale and apply it to width and height
    node.scaleX(1);
    node.scaleY(1);

    onChange({
      x: node.x(),
      y: node.y(),
      width: Math.max(5, node.width() * scaleX),
      height: Math.max(5, node.height() * scaleY),
      rotation: node.rotation()
    });
  };

  // Don't render if image is not loaded
  if (!image) return null;

  // Apply filters based on the filters array
  const konvaFilters = [];
  if (filters.includes('grayscale')) {
    konvaFilters.push(Konva.Filters.Grayscale);
  }
  if (filters.includes('invert')) {
    konvaFilters.push(Konva.Filters.Invert);
  }
  if (filters.includes('brighten')) {
    konvaFilters.push(Konva.Filters.Brighten);
  }

  return (
    <>
      <Image
        ref={imageRef}
        x={x}
        y={y}
        image={image}
        width={width}
        height={height}
        draggable
        rotation={rotation}
        opacity={opacity}
        globalCompositeOperation={blendMode}
        filters={konvaFilters}
        brightness={filters.includes('brighten') ? 0.2 : 0}
        onClick={onSelect}
        onTap={onSelect}
        onDragEnd={handleDragEnd}
        onTransformEnd={handleTransformEnd}
      />
      {isSelected && (
        <Transformer
          ref={trRef}
          boundBoxFunc={(oldBox, newBox) => {
            // Limit resize
            if (newBox.width < 5 || newBox.height < 5) {
              return oldBox;
            }
            return newBox;
          }}
        />
      )}
    </>
  );
};

// Design element wrapper that renders the appropriate element type
const DesignElement = (props) => {
  return props.type === 'text'
    ? <TextElement {...props} />
    : <ImageElement {...props} />;
};

const TShirtDesigner = ({ isAuthenticated }) => {
  // Canvas state
  const stageRef = useRef(null);
  const [stageSize, setStageSize] = useState({ width: 500, height: 600 });

  // Design elements state
  const [elements, setElements] = useState([]);
  const [selectedId, setSelectedId] = useState(null);

  // T-shirt options
  const [tshirtColor, setTshirtColor] = useState('#ffffff');
  const [tshirtSize, setTshirtSize] = useState('M');
  const [tshirtView, setTshirtView] = useState('front');
  const [tshirtGender, setTshirtGender] = useState('male');

  // Update price based on t-shirt color (colored shirts cost more)
  const updateTshirtColor = (color) => {
    setTshirtColor(color);
    // White shirts are standard price, colored shirts cost more
    setPrice(color === '#ffffff' ? 250 : 275);
  };

  // Text options
  const [currentText, setCurrentText] = useState('sample');
  const [currentFont, setCurrentFont] = useState('Montserrat');
  const [textColor, setTextColor] = useState('#000000');
  const [fontSize, setFontSize] = useState(30);
  const [textRotation, setTextRotation] = useState(0);

  // Order details
  const [designName, setDesignName] = useState('My T-Shirt Design');
  const [price, setPrice] = useState(250); // Base price in PHP
  const [quantity, setQuantity] = useState(1);
  const [totalPrice, setTotalPrice] = useState(250);
  const [addedToCart, setAddedToCart] = useState(false);

  // Initialize stage size
  useEffect(() => {
    const checkSize = () => {
      const container = document.querySelector('.designer-canvas-container');
      if (container) {
        // Maintain a 5:6 aspect ratio for the canvas (t-shirt proportions)
        const maxWidth = Math.min(container.offsetWidth - 40, 500);
        const maxHeight = Math.min(container.offsetHeight - 40, 600);

        // Calculate dimensions while maintaining aspect ratio
        let width, height;
        if (maxWidth / maxHeight > 5/6) {
          // Container is wider than our target ratio
          height = maxHeight;
          width = height * (5/6);
        } else {
          // Container is taller than our target ratio
          width = maxWidth;
          height = width * (6/5);
        }

        setStageSize({ width, height });
      }
    };

    checkSize();
    window.addEventListener('resize', checkSize);
    return () => window.removeEventListener('resize', checkSize);
  }, []);

  // Update total price when quantity or base price changes
  useEffect(() => {
    setTotalPrice(price * quantity);
  }, [price, quantity]);

  // Check for clicks outside of Konva elements to deselect
  useEffect(() => {
    const checkDeselect = (e) => {
      if (e.target === e.currentTarget) {
        setSelectedId(null);
      }
    };

    const container = document.querySelector('.konvajs-content');
    if (container) {
      container.addEventListener('mousedown', checkDeselect);
      return () => container.removeEventListener('mousedown', checkDeselect);
    }
  }, []);

  // Handle text change
  const handleTextChange = (e) => {
    setCurrentText(e.target.value);

    // If a text element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'text') {
        updateElement(selectedId, { text: e.target.value });
      }
    }
  };

  // Handle font change
  const handleFontChange = (font) => {
    setCurrentFont(font);

    // If a text element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'text') {
        updateElement(selectedId, { fontFamily: font });
      }
    }
  };

  // Handle text color change
  const handleTextColorChange = (e) => {
    const newColor = e.target.value;
    setTextColor(newColor);

    // If a text element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'text') {
        updateElement(selectedId, { fill: newColor });
      }
    }
  };

  // Handle font size change
  const handleFontSizeChange = (e) => {
    // Make sure we have a valid number
    const value = e.target.value;
    if (value === '' || isNaN(value)) return;

    const newSize = parseInt(value);
    if (newSize < 8 || newSize > 72) return;

    setFontSize(newSize);

    // If a text element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'text') {
        updateElement(selectedId, { fontSize: newSize });
      }
    }
  };

  // Handle text rotation change
  const handleRotationChange = (e) => {
    const newRotation = parseInt(e.target.value);
    setTextRotation(newRotation);

    // If any element is selected, update its rotation
    if (selectedId) {
      updateElement(selectedId, { rotation: newRotation });
    }
  };

  // Add text to canvas
  const addText = () => {
    const newElement = {
      id: Date.now().toString(),
      type: 'text',
      x: stageSize.width / 2 - 50,
      y: stageSize.height / 2 - 10,
      text: currentText,
      fontSize: fontSize,
      fontFamily: currentFont,
      fill: textColor,
      rotation: textRotation,
    };

    setElements([...elements, newElement]);
    setSelectedId(newElement.id);
  };

  // Image options
  const [imageOpacity, setImageOpacity] = useState(1);
  const [imageBlendMode, setImageBlendMode] = useState('normal');
  const [imageFilters, setImageFilters] = useState([]);

  // Handle image upload
  const handleImageUpload = (imageUrl) => {
    try {
      // Create a DOM image element to get dimensions
      const img = document.createElement('img');

      img.onload = () => {
        // Scale image to fit canvas
        const maxDimension = 200;
        let width = img.width;
        let height = img.height;

        if (width > maxDimension || height > maxDimension) {
          const scaleFactor = Math.min(maxDimension / width, maxDimension / height);
          width *= scaleFactor;
          height *= scaleFactor;
        }

        const newElement = {
          id: Date.now().toString(),
          type: 'image',
          x: stageSize.width / 2 - width / 2,
          y: stageSize.height / 2 - height / 2,
          width: width,
          height: height,
          src: imageUrl,
          rotation: 0,
          opacity: imageOpacity,
          blendMode: imageBlendMode,
          filters: [...imageFilters],
        };

        setElements([...elements, newElement]);
        setSelectedId(newElement.id);
      };

      img.onerror = (error) => {
        console.error("Error loading image:", error);
        alert("Failed to load the image. Please try another image.");
      };

      img.src = imageUrl;
    } catch (error) {
      console.error("Error in handleImageUpload:", error);
      alert("An error occurred while processing the image.");
    }
  };

  // Handle image opacity change
  const handleOpacityChange = (e) => {
    const newOpacity = parseFloat(e.target.value);
    setImageOpacity(newOpacity);

    // If an image element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'image') {
        updateElement(selectedId, { opacity: newOpacity });
      }
    }
  };

  // Handle image blend mode change
  const handleBlendModeChange = (mode) => {
    setImageBlendMode(mode);

    // If an image element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'image') {
        updateElement(selectedId, { blendMode: mode });
      }
    }
  };

  // Toggle image filter
  const toggleImageFilter = (filter) => {
    let newFilters;
    if (imageFilters.includes(filter)) {
      newFilters = imageFilters.filter(f => f !== filter);
    } else {
      newFilters = [...imageFilters, filter];
    }

    setImageFilters(newFilters);

    // If an image element is selected, update it
    if (selectedId) {
      const selectedElement = elements.find(el => el.id === selectedId);
      if (selectedElement && selectedElement.type === 'image') {
        updateElement(selectedId, { filters: newFilters });
      }
    }
  };

  // Update an element
  const updateElement = (id, newAttrs) => {
    const updatedElements = elements.map(el => {
      if (el.id === id) {
        return { ...el, ...newAttrs };
      }
      return el;
    });

    setElements(updatedElements);
  };

  // Delete selected element
  const deleteSelected = () => {
    if (selectedId) {
      setElements(elements.filter(el => el.id !== selectedId));
      setSelectedId(null);
    }
  };

  // Clear all elements
  const clearCanvas = () => {
    setElements([]);
    setSelectedId(null);
  };

  // Save design as image
  const saveDesign = () => {
    if (stageRef.current) {
      const dataUrl = stageRef.current.toDataURL();
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = `${designName.replace(/\s+/g, '-').toLowerCase()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Add to cart
  const addToCart = () => {
    if (stageRef.current) {
      // Get design as data URL
      const designImage = stageRef.current.toDataURL();

      // Create cart item
      const cartItem = {
        id: Date.now().toString(),
        type: 'tshirt',
        name: designName,
        size: tshirtSize,
        color: tshirtColor,
        gender: tshirtGender,
        view: tshirtView,
        design: designImage,
        quantity: quantity,
        price: price,
        totalPrice: totalPrice,
      };

      // Get existing cart or initialize new one
      const existingCart = JSON.parse(localStorage.getItem('pixelprints-cart')) || [];

      // Add new item to cart
      const updatedCart = [...existingCart, cartItem];

      // Save to localStorage
      localStorage.setItem('pixelprints-cart', JSON.stringify(updatedCart));

      // Show success message
      setAddedToCart(true);

      // Reset after 3 seconds
      setTimeout(() => {
        setAddedToCart(false);
      }, 3000);
    }
  };

  return (
    <div className="tshirt-designer-container">
      <div className="designer-tools-panel">
        <div className="tool-panel">
          <h3>Design Tools</h3>

          <div className="text-editor-panel">
            <h4>Edit Text</h4>
            <div className="text-input-container">
              <input
                type="text"
                value={currentText}
                onChange={handleTextChange}
                placeholder="Enter text here"
                className="text-input"
              />
              <button
                className="add-text-button"
                onClick={addText}
              >
                Add Text
              </button>
            </div>

            <div className="font-selector">
              <label>Change Font</label>
              <div className="font-dropdown">
                <select
                  value={currentFont}
                  onChange={(e) => handleFontChange(e.target.value)}
                >
                  {/* Modern Sans-Serif Fonts */}
                  <optgroup label="Modern Sans-Serif">
                    <option value="Montserrat">Montserrat</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Poppins">Poppins</option>
                    <option value="Raleway">Raleway</option>
                    <option value="Lato">Lato</option>
                  </optgroup>

                  {/* Display & Decorative Fonts */}
                  <optgroup label="Display & Decorative">
                    <option value="Bebas Neue">Bebas Neue</option>
                    <option value="Pacifico">Pacifico</option>
                    <option value="Permanent Marker">Permanent Marker</option>
                    <option value="Bangers">Bangers</option>
                    <option value="Fredoka One">Fredoka One</option>
                  </optgroup>

                  {/* Classic Fonts */}
                  <optgroup label="Classic">
                    <option value="Arial">Arial</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Courier New">Courier New</option>
                  </optgroup>
                </select>
              </div>
            </div>

            <div className="text-color-control">
              <label>Edit Color</label>
              <input
                type="color"
                value={textColor}
                onChange={handleTextColorChange}
                className="color-picker"
              />
              <span className="color-label">Black</span>
            </div>

            <div className="text-size-control">
              <label htmlFor="text-size">Text Size:</label>
              <input
                id="text-size"
                type="number"
                min="8"
                max="72"
                step="1"
                value={fontSize}
                onChange={handleFontSizeChange}
              />
              <div className="size-buttons">
                <button type="button" onClick={() => handleFontSizeChange({ target: { value: Math.max(8, fontSize - 2) } })}>-</button>
                <button type="button" onClick={() => handleFontSizeChange({ target: { value: Math.min(72, fontSize + 2) } })}>+</button>
              </div>
            </div>

            <div className="rotation-control">
              <label htmlFor="rotation">Rotation:</label>
              <input
                id="rotation"
                type="range"
                min="0"
                max="360"
                value={textRotation}
                onChange={handleRotationChange}
              />
              <span>{textRotation}°</span>
            </div>

            {selectedId && (
              <button
                className="delete-button"
                onClick={deleteSelected}
              >
                Delete Selected
              </button>
            )}
          </div>

          <div className="divider"></div>

          <ImageUploader onImageUpload={handleImageUpload} />

          {/* Image editing options - only show when an image is selected */}
          {selectedId && elements.find(el => el.id === selectedId && el.type === 'image') && (
            <div className="image-editor-panel">
              <h4>Edit Image</h4>

              <div className="opacity-control">
                <label htmlFor="opacity">Opacity:</label>
                <input
                  id="opacity"
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={elements.find(el => el.id === selectedId).opacity || 1}
                  onChange={handleOpacityChange}
                />
                <span>{Math.round((elements.find(el => el.id === selectedId).opacity || 1) * 100)}%</span>
              </div>

              <div className="blend-mode-control">
                <label>Blend Mode:</label>
                <div className="blend-mode-buttons">
                  {['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten'].map(mode => (
                    <button
                      key={mode}
                      className={`blend-mode-button ${elements.find(el => el.id === selectedId).blendMode === mode ? 'active' : ''}`}
                      onClick={() => handleBlendModeChange(mode)}
                    >
                      {mode.charAt(0).toUpperCase() + mode.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="filter-control">
                <label>Image Filters:</label>
                <div className="filter-buttons">
                  {[
                    { id: 'grayscale', label: 'Grayscale' },
                    { id: 'invert', label: 'Invert' },
                    { id: 'brighten', label: 'Brighten' }
                  ].map(filter => (
                    <button
                      key={filter.id}
                      className={`filter-button ${elements.find(el => el.id === selectedId).filters?.includes(filter.id) ? 'active' : ''}`}
                      onClick={() => toggleImageFilter(filter.id)}
                    >
                      {filter.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="divider"></div>

          <button
            className="clear-button"
            onClick={clearCanvas}
            title="Clear Canvas"
          >
            Clear All Designs
          </button>
        </div>
      </div>

      <div className="designer-canvas-container">
        <Stage
          width={stageSize.width}
          height={stageSize.height}
          ref={stageRef}
          onClick={(e) => {
            // Deselect when clicking on empty area
            if (e.target === e.target.getStage()) {
              setSelectedId(null);
            }
          }}
        >
          <Layer>
            {/* Background matching t-shirt color */}
            <Rect
              x={0}
              y={0}
              width={stageSize.width}
              height={stageSize.height}
              fill={tshirtColor}
              stroke="#dddddd"
              strokeWidth={1}
            />

            {/* Design elements */}
            {elements.map((element) => (
              <DesignElement
                key={element.id}
                id={element.id}
                type={element.type}
                x={element.x}
                y={element.y}
                width={element.width}
                height={element.height}
                text={element.text}
                fontSize={element.fontSize}
                fontFamily={element.fontFamily}
                fill={element.fill}
                rotation={element.rotation}
                src={element.src}
                isSelected={element.id === selectedId}
                onSelect={() => setSelectedId(element.id)}
                onChange={(newAttrs) => updateElement(element.id, newAttrs)}
              />
            ))}
          </Layer>
        </Stage>
      </div>

      <div className="designer-preview-panel">
        <div className="view-options">
          <button
            className={`view-button ${tshirtView === 'front' ? 'active' : ''}`}
            onClick={() => setTshirtView('front')}
          >
            <img src="https://www.customink.com/assets/site_images/products/116200/primary-116200_white_front_thumb-e0c2b5e8c7a4e7c7e5a1b4c8c7a4e7c7.jpg" alt="Front" />
            <span>Front</span>
          </button>
          <button
            className={`view-button ${tshirtView === 'back' ? 'active' : ''}`}
            onClick={() => setTshirtView('back')}
          >
            <img src="https://www.customink.com/assets/site_images/products/116200/primary-116200_white_back_thumb-e0c2b5e8c7a4e7c7e5a1b4c8c7a4e7c7.jpg" alt="Back" />
            <span>Back</span>
          </button>
          <button
            className={`view-button ${tshirtView === 'sleeve' ? 'active' : ''}`}
            onClick={() => setTshirtView('sleeve')}
          >
            <img src="https://www.customink.com/assets/site_images/products/116200/primary-116200_white_side_thumb-e0c2b5e8c7a4e7c7e5a1b4c8c7a4e7c7.jpg" alt="Sleeve" />
            <span>Sleeve Design</span>
          </button>
        </div>

        <div className="tshirt-preview">
          <h3>T-Shirt Preview</h3>

          <div className="model-selector">
            <button
              className={`model-button ${tshirtGender === 'male' ? 'active' : ''}`}
              onClick={() => setTshirtGender('male')}
            >
              Male Model
            </button>
            <button
              className={`model-button ${tshirtGender === 'female' ? 'active' : ''}`}
              onClick={() => setTshirtGender('female')}
            >
              Female Model
            </button>
          </div>

          <div className="tshirt-mockup">
            <TShirtModel
              color={tshirtColor}
              gender={tshirtGender}
              view={tshirtView}
            />

            {/* Design overlay */}
            <div className="design-overlay">
              {elements.length > 0 && stageRef.current ? (
                <img
                  src={stageRef.current.toDataURL({ pixelRatio: 2 })}
                  alt="Your design"
                  className="design-preview-image"
                  style={{
                    mixBlendMode: tshirtColor === '#ffffff' ? 'normal' : 'multiply',
                    opacity: tshirtColor === '#ffffff' ? 1 : 0.9
                  }}
                />
              ) : (
                <div className="design-placement-guide" style={{ borderColor: tshirtColor === '#ffffff' ? '#ddd' : '#fff' }}>
                  <span style={{ color: tshirtColor === '#ffffff' ? '#666' : '#fff' }}>
                    Design Area
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="tshirt-options">
            <div className="color-options">
              <label>T-Shirt Color:</label>
              <div className="color-swatches">
                {['#ffffff', '#000000', '#000080', '#ff0000', '#008000', '#ffff00', '#800080', '#808080'].map((color) => (
                  <div
                    key={color}
                    className={`color-swatch ${tshirtColor === color ? 'selected' : ''}`}
                    style={{ backgroundColor: color }}
                    onClick={() => updateTshirtColor(color)}
                    title={color === '#ffffff' ? 'White' :
                           color === '#000000' ? 'Black' :
                           color === '#000080' ? 'Navy' :
                           color === '#ff0000' ? 'Red' :
                           color === '#008000' ? 'Green' :
                           color === '#ffff00' ? 'Yellow' :
                           color === '#800080' ? 'Purple' : 'Gray'}
                  />
                ))}
              </div>
            </div>

            <div className="size-options">
              <label>Size:</label>
              <div className="size-buttons">
                {['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL'].map((size) => (
                  <button
                    key={size}
                    className={`size-button ${tshirtSize === size ? 'selected' : ''}`}
                    onClick={() => setTshirtSize(size)}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="design-details">
          <div className="design-name-container">
            <label htmlFor="design-name">Design Name:</label>
            <input
              id="design-name"
              type="text"
              value={designName}
              onChange={(e) => setDesignName(e.target.value)}
              maxLength={50}
            />
          </div>

          <div className="quantity-container">
            <label htmlFor="quantity">Quantity:</label>
            <input
              id="quantity"
              type="number"
              min="1"
              max="100"
              value={quantity}
              onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
            />
          </div>

          <div className="price-container">
            <p>Price per shirt: ₱{price.toFixed(2)}</p>
            <p className="total-price">Total: ₱{totalPrice.toFixed(2)}</p>
          </div>

          <div className="action-buttons">
            <button className="save-button" onClick={saveDesign}>
              Save Design
            </button>
            <button
              className="add-to-cart-button"
              onClick={addToCart}
              disabled={!isAuthenticated}
            >
              Add to Cart
            </button>
          </div>

          {addedToCart && (
            <div className="success-message">
              Design added to cart successfully!
            </div>
          )}

          {!isAuthenticated && (
            <p className="login-required-message">
              Please login to add designs to your cart
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TShirtDesigner;
