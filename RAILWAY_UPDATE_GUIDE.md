# Railway Environment Variables Update Guide

To ensure your backend properly connects with your Netlify frontend at https://pixelprints.it.com, follow these steps to update your Railway environment variables:

## 1. Log in to Railway Dashboard

Go to [Railway Dashboard](https://railway.app/dashboard) and log in to your account.

## 2. Select Your Project

Find and select your `pixel-prints-backend` project.

## 3. Navigate to Variables Tab

Click on the "Variables" tab in your project dashboard.

## 4. Update Environment Variables

Update or add the following environment variables:

```
# Frontend URL for CORS
FRONTEND_URL=https://pixelprints.it.com

# JWT Secret for Authentication
JWT_SECRET=pixel-prints-secure-jwt-key-2024

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
```

## 5. Save Changes

Click the "Save" button to apply your changes.

## 6. Redeploy Your Application

After saving the environment variables, Railway will automatically redeploy your application with the new settings.

## 7. Verify CORS Configuration

The CORS configuration in your server.js file should now allow requests from your Netlify domain:

```javascript
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))
```

## 8. Test Admin Login

Once the deployment is complete, try logging in to the admin dashboard at https://pixelprints.it.com/admin-login with:

- Email: <EMAIL>
- Password: admin123

If you still can't log in, you may need to recreate the admin user by running:

```bash
npm run recreate-admin
```

in your Railway terminal or by adding a one-time script to your deployment.
