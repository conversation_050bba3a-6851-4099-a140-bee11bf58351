import React, { useState } from 'react';
import { getApiUrl } from '../../utils/api';
import './StatusUpdateForm.css';

const StatusUpdateForm = ({ orderId, currentStatus, onStatusUpdate, onCancel }) => {
  const [status, setStatus] = useState(currentStatus || 'pending');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const statusOptions = [
    { value: 'pending', label: 'Pending', description: 'Order has been received but not yet processed' },
    { value: 'processing', label: 'Processing', description: 'Order is being prepared' },
    { value: 'completed', label: 'Completed', description: 'Order has been delivered and completed' },
    { value: 'delayed', label: 'Delayed', description: 'Order is delayed due to unforeseen circumstances' },
    { value: 'cancelled', label: 'Cancelled', description: 'Order has been cancelled' }
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsSubmitting(true);

    try {
      const response = await fetch(getApiUrl(`/api/orders/${orderId}/status`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update status');
      }

      onStatusUpdate(data.order);
    } catch (error) {
      console.error('Error updating status:', error);
      setError(error.message || 'Failed to update status');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="status-update-form">
      <h3>Update Order Status</h3>
      {error && <p className="error-message">{error}</p>}
      <form onSubmit={handleSubmit}>
        <div className="status-options">
          {statusOptions.map((option) => (
            <div
              key={option.value}
              className={`status-option ${status === option.value ? 'selected' : ''}`}
              onClick={() => setStatus(option.value)}
            >
              <div className="status-option-header">
                <input
                  type="radio"
                  id={option.value}
                  name="status"
                  value={option.value}
                  checked={status === option.value}
                  onChange={() => setStatus(option.value)}
                />
                <label htmlFor={option.value}>{option.label}</label>
              </div>
              <p className="status-description">{option.description}</p>
            </div>
          ))}
        </div>
        <div className="form-actions">
          <button
            type="button"
            className="cancel-button"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="submit-button"
            disabled={isSubmitting || status === currentStatus}
          >
            {isSubmitting ? 'Updating...' : 'Update Status'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default StatusUpdateForm;
