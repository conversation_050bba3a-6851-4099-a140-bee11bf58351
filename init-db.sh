#!/bin/bash

# Wait for the database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Initialize the database
echo "Initializing database..."
docker-compose exec backend node db/init.js
docker-compose exec backend node db/setup_settings.js
docker-compose exec backend node db/create_admin.js
docker-compose exec backend node db/add_site_notification_settings.js

echo "Database initialization complete!"
