{"name": "pixel-prints-backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node start.js", "init-db": "node db/init.js", "setup-settings": "node db/setup_settings.js", "create-admin": "node db/create_admin.js", "recreate-admin": "node db/recreate_admin.js", "update-users": "node db/update_users_reset_token.js", "add-site-notification": "node db/add_site_notification_settings.js", "dev": "nodemon server.js", "postinstall": "echo 'Skipping database initialization during build'"}, "keywords": ["printing", "document-printing", "photo-printing", "delivery", "ecommerce"], "author": "Pixel Prints", "license": "ISC", "description": "Backend API for Pixel Prints document and photo printing service", "dependencies": {"axios": "^1.8.4", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "pg": "^8.14.1"}, "devDependencies": {"nodemon": "^3.1.9"}}