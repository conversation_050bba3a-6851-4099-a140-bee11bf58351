# Deploying Pixel Prints to Render using Docker

This guide explains how to deploy the Pixel Prints application to Render using Docker, without requiring a credit card.

## Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/) installed locally (for testing)
- [Render CLI](https://render.com/docs/cli) installed (optional, for automated deployment)
- A Render account (free tier)
- A GitHub account (for continuous deployment)

## Deployment Options

### Option 1: Using the Render Dashboard (No Credit Card Required)

1. **Create a Render Account**:
   - Go to [Render](https://render.com/) and sign up for a free account
   - No credit card is required for the free tier

2. **Create a New Web Service**:
   - Click "New" and select "Web Service"
   - Connect your GitHub repository
   - Select the repository containing your Pixel Prints application

3. **Configure the Web Service**:
   - Select "Docker" as the runtime
   - Set the root directory to `pixel-prints-backend`
   - Set the Docker command to `node server.js`
   - Choose the free plan
   - Add the following environment variables:
     - `NODE_ENV`: `production`
     - `FRONTEND_URL`: `https://jocular-jelly-4e9cef.netlify.app`
     - `JWT_SECRET`: `pixel-prints-secure-jwt-key-2024`
     - `ADMIN_EMAIL`: `<EMAIL>`
     - `ADMIN_PASSWORD`: `admin123`
     - `EMAIL_USER`: `<EMAIL>`
     - `EMAIL_PASS`: `your-email-password`

4. **Create a PostgreSQL Database**:
   - Click "New" and select "PostgreSQL"
   - Choose the free plan
   - Note the connection string for the next step

5. **Update the Web Service**:
   - Add the `DATABASE_URL` environment variable with the connection string from the previous step

6. **Deploy the Service**:
   - Click "Create Web Service"
   - Wait for the deployment to complete

7. **Initialize the Database**:
   - Go to the "Shell" tab of your web service
   - Run the following commands:
     ```
     node db/init.js
     node db/setup_settings.js
     node db/create_admin.js
     node db/add_site_notification_settings.js
     ```

### Option 2: Using the Render CLI and Blueprint (render.yaml)

1. **Install the Render CLI**:
   ```bash
   curl -s https://render.com/download-cli/main | bash
   ```

2. **Login to Render**:
   ```bash
   render login
   ```

3. **Deploy using the Blueprint**:
   ```bash
   render blueprint apply
   ```

4. **Initialize the Database**:
   ```bash
   render run --service pixel-prints-backend 'node db/init.js'
   render run --service pixel-prints-backend 'node db/setup_settings.js'
   render run --service pixel-prints-backend 'node db/create_admin.js'
   render run --service pixel-prints-backend 'node db/add_site_notification_settings.js'
   ```

### Option 3: Using the Deployment Scripts

We've provided deployment scripts for both PowerShell and Bash:

#### PowerShell (Windows)

```powershell
.\deploy-to-render-docker.ps1
```

#### Bash (Linux/Mac)

```bash
chmod +x deploy-to-render-docker.sh
./deploy-to-render-docker.sh
```

## Testing Locally with Docker

Before deploying to Render, you can test your application locally using Docker:

1. **Build and Run with Docker Compose**:
   ```bash
   docker-compose up --build
   ```

2. **Access the Application**:
   - Backend: http://localhost:3001
   - API Test: http://localhost:3001/api/test

3. **Initialize the Database**:
   ```bash
   docker-compose exec backend node db/init.js
   docker-compose exec backend node db/setup_settings.js
   docker-compose exec backend node db/create_admin.js
   docker-compose exec backend node db/add_site_notification_settings.js
   ```

## Updating the Frontend

After deploying the backend to Render, you need to update the frontend configuration:

1. **Update Frontend Configuration**:
   ```bash
   # Windows
   .\update-frontend-for-render-docker.ps1 -BackendUrl "https://pixel-prints-backend.onrender.com"

   # Linux/Mac
   ./update-frontend-for-render-docker.sh "https://pixel-prints-backend.onrender.com"
   ```

2. **Deploy to Netlify**:
   ```bash
   cd pixelprints-app
   npm run build
   netlify deploy --prod
   ```

## Testing CORS

After deployment, you can test CORS using the provided test file:

1. **Open the CORS Test File**:
   ```bash
   # Windows
   Start-Process .\cors-test-render.html

   # Linux/Mac
   open cors-test-render.html
   ```

2. **Enter Your Backend URL**:
   - Backend URL: https://pixel-prints-backend.onrender.com
   - Frontend URL: https://jocular-jelly-4e9cef.netlify.app

3. **Run the Tests**:
   - Click "Test Backend" to verify the backend is accessible
   - Click "Test CORS" to verify CORS is working correctly
   - Click "Test Auth" to verify authentication is working

## Free Tier Limitations

The Render free tier has some limitations to be aware of:

1. **Spin Down After Inactivity**:
   - Free tier services will spin down after 15 minutes of inactivity
   - They'll spin back up when a request comes in (causing a brief delay)

2. **Limited Resources**:
   - 512 MB RAM
   - Shared CPU
   - Limited bandwidth

3. **No Persistent Disk Storage**:
   - Files uploaded to the service will be lost when the service restarts
   - Use external storage services for file uploads

4. **PostgreSQL Limitations**:
   - 1 GB storage
   - Shared CPU
   - No automatic backups

## Troubleshooting

### Common Issues

1. **CORS Issues**:
   - Verify that the `FRONTEND_URL` environment variable is set correctly
   - Check the CORS configuration in server.js
   - Update the Content Security Policy in your frontend's netlify.toml file

2. **Database Connection Issues**:
   - Verify that the `DATABASE_URL` environment variable is set correctly
   - Check the Render logs for any database connection errors

3. **Deployment Failures**:
   - Check the Render logs for any errors
   - Verify that your Dockerfile is correct
   - Make sure your application starts correctly

### Getting Help

If you encounter any issues, you can:

1. Check the [Render documentation](https://render.com/docs)
2. Join the [Render Community](https://community.render.com/)
3. Contact [Render Support](https://render.com/support)
