import React, { useState, useEffect } from 'react';
import './UserDashboard.css';
import OrderCard from './OrderCard';
import StatusUpdateForm from './StatusUpdateForm';
import CommentSection from './CommentSection';
import { getApiUrl } from '../../utils/api';
import logger from '../../utils/logger';

const UserDashboard = ({ userEmail, userName, onBackToMain }) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [viewMode, setViewMode] = useState('all'); // 'all', 'pending', 'completed'
  const [showStatusForm, setShowStatusForm] = useState(false);
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalSpent: 0
  });

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        // Use the getApiUrl function instead of hardcoding the URL
        const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
        // This will be replaced with getApiUrl in the fetch call

        try {
          // Try to fetch from backend
          logger.info('Fetching orders for user:', userEmail);
          const url = getApiUrl(`/api/orders/customer/${encodeURIComponent(userEmail)}`);
          logger.debug('Fetch URL:', url);

          const response = await fetch(url);
          logger.debug('Response status:', response.status);

          if (!response.ok) {
            throw new Error('Failed to fetch orders');
          }

          const data = await response.json();
          logger.debug('Orders data:', data);
          setOrders(data);

          // Calculate stats
          const totalOrders = data.length;
          const pendingOrders = data.filter(order => order.status === 'pending').length;
          const completedOrders = data.filter(order => order.status === 'completed').length;
          const totalSpent = data.reduce((sum, order) => sum + parseFloat(order.price || 0), 0);

          setStats({
            totalOrders,
            pendingOrders,
            completedOrders,
            totalSpent
          });
        } catch (apiError) {
          logger.warn('Backend API not available, using mock data');
          logger.error('API Error:', apiError);
          // Use mock data if backend is not available
          const mockOrders = [
            {
              id: 1,
              order_number: 'ORD-001',
              customer_name: userName || 'Test User',
              customer_email: userEmail,
              print_type: 'black',
              total_pages: 10,
              price: '100.00',
              address: '123 Test Street, Test City',
              province: 'Test Province',
              status: 'pending',
              created_at: new Date().toISOString()
            },
            {
              id: 2,
              order_number: 'ORD-002',
              customer_name: userName || 'Test User',
              customer_email: userEmail,
              print_type: 'colored',
              total_pages: 5,
              price: '80.00',
              address: '123 Test Street, Test City',
              province: 'Test Province',
              status: 'completed',
              created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
            },
            {
              id: 3,
              order_number: 'ORD-003',
              customer_name: userName || 'Test User',
              customer_email: userEmail,
              print_type: 'black',
              total_pages: 20,
              price: '200.00',
              address: '123 Test Street, Test City',
              province: 'Test Province',
              status: 'processing',
              created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
            }
          ];

          setOrders(mockOrders);

          // Calculate stats from mock data
          const totalOrders = mockOrders.length;
          const pendingOrders = mockOrders.filter(order => order.status === 'pending').length;
          const completedOrders = mockOrders.filter(order => order.status === 'completed').length;
          const totalSpent = mockOrders.reduce((sum, order) => sum + parseFloat(order.price || 0), 0);

          setStats({
            totalOrders,
            pendingOrders,
            completedOrders,
            totalSpent
          });
        }
      } catch (err) {
        logger.error('Error fetching orders:', err);
        setError('Failed to load your orders. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (userEmail) {
      fetchOrders();
    } else {
      setLoading(false);
    }
  }, [userEmail, userName]);

  // Filter orders based on viewMode
  const filteredOrders = orders.filter(order => {
    if (viewMode === 'all') return true;
    return order.status.toLowerCase() === viewMode;
  });

  // Handle order click to view details
  const handleOrderClick = (order) => {
    setSelectedOrder(order);
  };

  // Close order details modal
  const closeOrderDetails = () => {
    setSelectedOrder(null);
    setShowStatusForm(false);
  };

  // Open status update form
  const openStatusForm = () => {
    setShowStatusForm(true);
  };

  // Handle status update
  const handleStatusUpdate = (updatedOrder) => {
    // Update the order in the orders array
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === updatedOrder.id ? updatedOrder : order
      )
    );

    // Update stats
    const updatedOrders = orders.map(order =>
      order.id === updatedOrder.id ? updatedOrder : order
    );
    const totalOrders = updatedOrders.length;
    const pendingOrders = updatedOrders.filter(order => order.status === 'pending').length;
    const completedOrders = updatedOrders.filter(order => order.status === 'completed').length;
    const totalSpent = updatedOrders.reduce((sum, order) => sum + parseFloat(order.price || 0), 0);

    setStats({
      totalOrders,
      pendingOrders,
      completedOrders,
      totalSpent
    });

    // Close the status form and show the updated order details
    setShowStatusForm(false);
    setSelectedOrder(updatedOrder);
  };

  // Handle delete order
  const handleDeleteOrder = async (orderId) => {
    setIsDeleting(true);
    try {
      try {
        // Try to delete from backend
        await fetch(getApiUrl(`/api/orders/${orderId}`), {
          method: 'DELETE',
        });
      } catch (apiError) {
        logger.warn('Backend API not available, using mock deletion');
      }

      // Update local state regardless of API success
      setOrders(orders.filter(order => order.id !== orderId && order.order_number !== orderId));
      setDeleteConfirmation(null);
      setSelectedOrder(null);

      // Recalculate stats
      const updatedOrders = orders.filter(order => order.id !== orderId && order.order_number !== orderId);
      const totalOrders = updatedOrders.length;
      const pendingOrders = updatedOrders.filter(order => order.status === 'pending').length;
      const completedOrders = updatedOrders.filter(order => order.status === 'completed').length;
      const totalSpent = updatedOrders.reduce((sum, order) => sum + parseFloat(order.price || 0), 0);

      setStats({
        totalOrders,
        pendingOrders,
        completedOrders,
        totalSpent
      });
    } catch (err) {
      logger.error('Error deleting order:', err);
    } finally {
      setIsDeleting(false);
    }
  };

  // Show delete confirmation
  const showDeleteConfirmation = (order) => {
    setDeleteConfirmation(order);
    setSelectedOrder(null);
    setShowStatusForm(false);
  };

  // Close delete confirmation
  const closeDeleteConfirmation = () => {
    setDeleteConfirmation(null);
  };

  if (!userEmail) {
    return <div className="login-prompt">Please log in to view your dashboard</div>;
  }

  return (
    <div className="user-dashboard-container">
      {/* Back Button */}
      <div className="dashboard-actions">
        <button className="back-button" onClick={onBackToMain}>
          ← Back to Main Page
        </button>
      </div>

      {/* Dashboard Header */}
      <div className="dashboard-header">
        <h1>Welcome, {userName || 'User'}</h1>
        <p>Here's your order summary</p>
      </div>

      {/* Dashboard Stats */}
      <div className="stats-container">
        <div className="stat-card">
          <h3>Total Orders</h3>
          <p className="stat-value">{stats.totalOrders}</p>
        </div>
        <div className="stat-card">
          <h3>Pending Orders</h3>
          <p className="stat-value">{stats.pendingOrders}</p>
        </div>
        <div className="stat-card">
          <h3>Completed Orders</h3>
          <p className="stat-value">{stats.completedOrders}</p>
        </div>
        <div className="stat-card">
          <h3>Total Spent</h3>
          <p className="stat-value">₱{stats.totalSpent.toFixed(2)}</p>
        </div>
      </div>

      {/* Order Filters */}
      <div className="order-filters">
        <h2>My Orders</h2>
        <div className="filter-buttons">
          <button
            className={viewMode === 'all' ? 'active' : ''}
            onClick={() => setViewMode('all')}
          >
            All Orders
          </button>
          <button
            className={viewMode === 'pending' ? 'active' : ''}
            onClick={() => setViewMode('pending')}
          >
            Pending
          </button>
          <button
            className={viewMode === 'processing' ? 'active' : ''}
            onClick={() => setViewMode('processing')}
          >
            Processing
          </button>
          <button
            className={viewMode === 'completed' ? 'active' : ''}
            onClick={() => setViewMode('completed')}
          >
            Completed
          </button>
        </div>
      </div>

      {/* Orders List */}
      <div className="orders-container">
        {loading ? (
          <div className="loading-message">Loading your orders...</div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : filteredOrders.length > 0 ? (
          filteredOrders.map((order) => (
            <div key={order.order_number || order.id} onClick={() => handleOrderClick(order)}>
              <OrderCard order={order} />
            </div>
          ))
        ) : (
          <div className="no-orders-message">
            {viewMode === 'all'
              ? 'No orders found. Start shopping to see your orders here!'
              : `No ${viewMode} orders found.`
            }
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="order-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Order #{selectedOrder.order_number || selectedOrder.id || 'Unknown'}</h2>
              <div className="modal-actions">
                <button
                  className="update-status-button"
                  onClick={openStatusForm}
                  title="Update Status"
                >
                  Update
                </button>
                <button
                  className="delete-button"
                  onClick={() => showDeleteConfirmation(selectedOrder)}
                  title="Delete Order"
                >
                  Delete
                </button>
                <button className="close-button" onClick={closeOrderDetails}>X</button>
              </div>
            </div>
            <div className="modal-body">
              <div className="order-info">
                <div className="info-group">
                  <h3>Order Status</h3>
                  <p className={`status-badge ${selectedOrder.status || 'pending'}`}>
                    {selectedOrder.status
                      ? selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)
                      : 'Pending'}
                  </p>
                </div>
                <div className="info-group">
                  <h3>Order Date</h3>
                  <p>
                    {selectedOrder.created_at
                      ? new Date(selectedOrder.created_at).toLocaleDateString()
                      : 'N/A'}
                  </p>
                </div>
                <div className="info-group">
                  <h3>Print Type</h3>
                  <p>
                    {selectedOrder.print_type
                      ? (selectedOrder.print_type === 'black' ? 'Black & White' : 'Colored')
                      : 'N/A'}
                  </p>
                </div>
                <div className="info-group">
                  <h3>Total Pages</h3>
                  <p>{selectedOrder.total_pages || 'N/A'}</p>
                </div>
                <div className="info-group">
                  <h3>Delivery Address</h3>
                  <p>{selectedOrder.address || 'N/A'}</p>
                </div>
                <div className="info-group">
                  <h3>Total Amount</h3>
                  <p className="price">
                    ₱{selectedOrder.price ? parseFloat(selectedOrder.price).toFixed(2) : '0.00'}
                  </p>
                </div>
              </div>

              {/* Status Update Form */}
              {showStatusForm ? (
                <StatusUpdateForm
                  orderId={selectedOrder.id}
                  currentStatus={selectedOrder.status}
                  onStatusUpdate={handleStatusUpdate}
                  onCancel={() => setShowStatusForm(false)}
                />
              ) : (
                <CommentSection
                  orderId={selectedOrder.id}
                  userName={userName || 'Customer'}
                />
              )}
            </div>
            <div className="modal-footer">
              <button className="close-button" onClick={closeOrderDetails}>CLOSE</button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirmation && (
        <div className="delete-confirmation-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h2>Confirm Deletion</h2>
              <button className="close-button" onClick={closeDeleteConfirmation}>X</button>
            </div>
            <div className="modal-body">
              <p>Are you sure you want to delete Order #{deleteConfirmation.order_number || deleteConfirmation.id || 'Unknown'}?</p>
              <p>This action cannot be undone.</p>
              <div className="confirmation-buttons">
                <button
                  className="cancel-button"
                  onClick={closeDeleteConfirmation}
                  disabled={isDeleting}
                >
                  CANCEL
                </button>
                <button
                  className="confirm-delete-button"
                  onClick={() => handleDeleteOrder(deleteConfirmation.id || deleteConfirmation.order_number)}
                  disabled={isDeleting}
                >
                  {isDeleting ? 'DELETING...' : 'DELETE'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDashboard;
