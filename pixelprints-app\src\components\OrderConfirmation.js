"use client"
import "./OrderConfirmation.css"

function OrderConfirmation({ orderNumber, customerEmail, onClose }) {
  return (
    <div className="order-confirmation">
      <div className="confirmation-content">
        <div className="confirmation-icon">✓</div>
        <h2>Order Confirmed!</h2>
        <p className="order-number">Order #{orderNumber}</p>
        <p>Thank you for your order with Pixel Prints!</p>
        <p>
          We've sent a confirmation email to <strong>{customerEmail}</strong> with your order details.
        </p>
        <p>You'll receive another email when your order is ready for delivery.</p>
        <button className="close-button" onClick={onClose}>
          Back to Home
        </button>
      </div>
    </div>
  )
}

export default OrderConfirmation

