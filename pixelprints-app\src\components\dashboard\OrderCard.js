import React from 'react';

function OrderCard({ order }) {
  // Helper function to format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Helper function to get status badge color
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="order-card">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold">Order #{order.orderNumber}</h3>
          <p className="text-gray-600">{formatDate(order.createdAt)}</p>
        </div>
        <span className={`status-badge ${getStatusColor(order.status)}`}>
          {order.status}
        </span>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-600">Print Type</p>
          <p className="font-medium">{order.printType}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Total Pages</p>
          <p className="font-medium">{order.totalPages}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Delivery Address</p>
          <p className="font-medium">{order.address}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Total Amount</p>
          <p className="font-medium">₱{parseFloat(order.price).toFixed(2)}</p>
        </div>
      </div>
    </div>
  );
}

export default OrderCard;