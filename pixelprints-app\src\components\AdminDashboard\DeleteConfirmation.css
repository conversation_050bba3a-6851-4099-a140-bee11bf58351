.delete-confirmation {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.delete-confirmation h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #d32f2f;
}

.delete-confirmation p {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.delete-confirmation .warning {
  color: #d32f2f;
  font-weight: 500;
}

.confirmation-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button,
.delete-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.delete-button {
  background-color: #d32f2f;
  color: white;
}

.delete-button:hover {
  background-color: #b71c1c;
}

.cancel-button:disabled,
.delete-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
