// This file redirects to the actual server.js in the pixel-prints-backend directory
console.log('Starting server from root directory...');
console.log('Redirecting to pixel-prints-backend/server.js');

// Import and run the actual server
try {
  require('./pixel-prints-backend/server.js');
} catch (error) {
  console.error('Error loading server.js from pixel-prints-backend:', error);
  process.exit(1);
}
