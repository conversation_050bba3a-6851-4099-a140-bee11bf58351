/* T-Shirt Designer Page Styles */

.tshirt-designer-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f9f9f9;
}

.designer-header {
  background-color: #1a1a1a;
  color: white;
  padding: 20px;
  text-align: center;
  position: relative;
}

.designer-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.designer-header p {
  margin: 10px 0 0;
  font-size: 1.2rem;
  opacity: 0.8;
}

.welcome-message {
  margin-top: 15px;
  font-weight: 500;
  color: #52c41a;
}

.login-prompt {
  margin-top: 15px;
  font-size: 1rem;
}

.login-prompt button {
  background: none;
  border: none;
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  margin: 0;
}

.login-prompt button:hover {
  color: #40a9ff;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: transparent;
  border: 1px solid white;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.designer-footer {
  background-color: #1a1a1a;
  color: white;
  padding: 20px;
  text-align: center;
  margin-top: auto;
}

.designer-footer p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tshirt-designer-container {
    flex-direction: column;
  }
  
  .designer-canvas-container {
    order: 1;
    min-width: auto;
  }
  
  .designer-tools-panel {
    order: 2;
    min-width: auto;
  }
  
  .designer-preview-panel {
    order: 3;
    min-width: auto;
  }
  
  .back-button {
    position: static;
    margin-top: 15px;
    display: block;
    width: 150px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Add Font Awesome icons */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');
