# Deploying to Railway using the CLI

This guide explains how to deploy the Pixel Prints backend to Railway using the Railway CLI, following Railway's recommended approach.

## Prerequisites

- [Node.js](https://nodejs.org/) installed
- [Railway CLI](https://docs.railway.com/develop/cli) installed
- A Railway account

## Installation

### Install Railway CLI

```bash
npm install -g @railway/cli
```

### Login to Railway

```bash
railway login
```

This will open a browser window for authentication.

## Deployment Steps

### Option 1: Using the Deployment Script

We've provided deployment scripts for both PowerShell and Bash:

#### PowerShell (Windows)

```powershell
.\deploy-to-railway-cli.ps1 -EmailPass "your-actual-email-password"
```

#### Bash (Linux/Mac)

```bash
chmod +x deploy-to-railway-cli.sh
./deploy-to-railway-cli.sh --email-pass "your-actual-email-password"
```

### Option 2: Manual Deployment

If you prefer to deploy manually, follow these steps:

1. **Navigate to the Backend Directory**

```bash
cd pixel-prints-backend
```

2. **Initialize a New Railway Project**

```bash
railway init --name pixel-prints-backend
```

3. **Add PostgreSQL Database**

```bash
railway add --plugin postgresql
```

4. **Set Environment Variables**

```bash
railway vars set NODE_ENV=production
railway vars set FRONTEND_URL=https://jocular-jelly-4e9cef.netlify.app
railway vars set JWT_SECRET=pixel-prints-secure-jwt-key-2024
railway vars set ADMIN_EMAIL=<EMAIL>
railway vars set ADMIN_PASSWORD=admin123
railway vars set EMAIL_USER=<EMAIL>
railway vars set EMAIL_PASS=your-email-password
```

5. **Deploy the Backend**

```bash
railway up
```

6. **Initialize the Database**

```bash
railway run "node db/init.js"
railway run "node db/setup_settings.js"
railway run "node db/create_admin.js"
railway run "node db/add_site_notification_settings.js"
```

7. **Get the Deployed URL**

```bash
railway status --json
```

## Updating the Frontend

After deploying the backend to Railway, you need to update the frontend configuration:

1. **Update Frontend Environment Variables**

```bash
# Windows
Set-Content -Path 'pixelprints-app/.env.production' -Value 'REACT_APP_API_URL=https://your-railway-url.up.railway.app'

# Linux/Mac
echo 'REACT_APP_API_URL=https://your-railway-url.up.railway.app' > pixelprints-app/.env.production
```

2. **Update Content Security Policy in netlify.toml**

Edit `pixelprints-app/netlify.toml` and update the Content-Security-Policy to include your Railway domain:

```toml
Content-Security-Policy = "default-src 'self'; connect-src 'self' https://*.railway.app https://*.up.railway.app https://your-railway-url.up.railway.app; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval';"
```

3. **Redeploy the Frontend to Netlify**

```bash
cd pixelprints-app
npm run build
netlify deploy --prod
```

## Key Differences from Render Deployment

Railway's deployment approach differs from Render in several ways:

1. **Project Initialization**: Railway uses `railway init` to create a new project, while Render requires creating a project through their CLI or dashboard.

2. **Database Setup**: Railway uses `railway add --plugin postgresql` to add a PostgreSQL database, while Render requires creating a database separately.

3. **Deployment Process**: Railway uses `railway up` to deploy your code, which automatically handles building and deploying your application.

4. **Environment Variables**: Railway uses `railway vars set` to set environment variables, while Render requires setting them through their CLI or dashboard.

5. **Database Initialization**: Railway uses `railway run` to run commands on your deployed service, while Render uses a separate jobs API.

## Troubleshooting

### Common Issues

1. **Authentication Issues**: If you encounter authentication issues, try logging out and logging back in:

```bash
railway logout
railway login
```

2. **Deployment Failures**: If your deployment fails, check the logs:

```bash
railway logs
```

3. **Database Connection Issues**: If your application can't connect to the database, verify that the `DATABASE_URL` environment variable is set correctly:

```bash
railway vars
```

4. **CORS Issues**: If you encounter CORS issues, make sure the `FRONTEND_URL` environment variable is set correctly and that your frontend's Content Security Policy includes your Railway domain.

### Getting Help

If you encounter any issues, you can:

1. Check the [Railway documentation](https://docs.railway.com/)
2. Join the [Railway Discord community](https://discord.gg/railway)
3. Open a [GitHub issue](https://github.com/railwayapp/cli/issues)
