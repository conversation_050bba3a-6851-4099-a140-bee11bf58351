/**
 * <PERSON><PERSON><PERSON> to update CORS configuration in server.js
 */

const fs = require('fs');
const path = require('path');

// Path to server.js
const serverJsPath = path.join(__dirname, 'pixel-prints-backend', 'server.js');

// Read the current content of server.js
let content = fs.readFileSync(serverJsPath, 'utf8');

// Update the CORS configuration
const oldCorsConfig = `// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))
app.use(express.json())`;

const newCorsConfig = `// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  optionsSuccessStatus: 204
}))

// Add OPTIONS handling for preflight requests
app.options('*', cors());

app.use(express.json())`;

// Replace the old CORS configuration with the new one
content = content.replace(oldCorsConfig, newCorsConfig);

// Write the updated content back to server.js
fs.writeFileSync(serverJsPath, content, 'utf8');

console.log('CORS configuration updated successfully!');
