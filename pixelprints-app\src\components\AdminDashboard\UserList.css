.user-list {
  width: 100%;
  margin-top: 20px;
}

.user-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.user-list-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.user-list-actions {
  display: flex;
  gap: 10px;
}

.refresh-button {
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #e0f2fe;
  border-color: #7dd3fc;
}

.refresh-button:disabled {
  background-color: #f1f5f9;
  color: #94a3b8;
  border-color: #cbd5e1;
  cursor: not-allowed;
}

.add-user-button {
  background-color: #ecfdf5;
  color: #047857;
  border: 1px solid #a7f3d0;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.add-user-button:hover {
  background-color: #d1fae5;
  border-color: #6ee7b7;
}

.user-list table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.user-list th,
.user-list td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.user-list th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.user-list tr:last-child td {
  border-bottom: none;
}

.user-list tr:hover {
  background-color: #f8f9fa;
}

.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.role-badge.admin {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.role-badge.user {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #90caf9;
}

.role-badge.customer {
  background-color: #fff3e0;
  color: #e65100;
  border: 1px solid #ffe0b2;
}

/* User count summary */
.user-count-summary {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.user-count-item {
  background-color: white;
  border-radius: 4px;
  padding: 10px 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-width: 100px;
}

.user-count-label {
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 5px;
}

.user-count-value {
  font-size: 1.5rem;
  font-weight: 600;
}

.user-count-item.admin .user-count-value {
  color: #2e7d32;
}

.user-count-item.user .user-count-value {
  color: #1976d2;
}

.user-count-item.customer .user-count-value {
  color: #e65100;
}

.user-count-item.total .user-count-value {
  color: #0f172a;
}

.no-users {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 1.1rem;
}

/* Action buttons in table */
.action-buttons {
  display: flex;
  gap: 8px;
  white-space: nowrap;
}

.edit-button,
.delete-button {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.edit-button {
  background-color: #e3f2fd;
  color: #1976d2;
}

.edit-button:hover {
  background-color: #bbdefb;
}

.delete-button {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-button:hover {
  background-color: #ffcdd2;
}

.delete-button:disabled {
  background-color: #f5f5f5;
  color: #9e9e9e;
  cursor: not-allowed;
}

/* Action message */
.action-message {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-weight: 500;
  animation: fadeIn 0.3s ease-in-out;
}

.action-message.success {
  background-color: #d1fae5;
  color: #047857;
  border: 1px solid #a7f3d0;
}

.action-message.error {
  background-color: #fee2e2;
  color: #b91c1c;
  border: 1px solid #fecaca;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}
