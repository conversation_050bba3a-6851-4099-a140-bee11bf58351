/**
 * <PERSON><PERSON><PERSON> to test admin login with CORS headers
 */

const axios = require('axios');

// Admin credentials
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

// API URL
const API_URL = 'https://pixel-prints-production.up.railway.app';

// Test admin login
async function testAdminLogin() {
  try {
    console.log('Testing admin login with credentials:', adminCredentials);
    
    const response = await axios.post(`${API_URL}/api/admin/login`, adminCredentials, {
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://jocular-jelly-4e9cef.netlify.app'
      }
    });
    
    console.log('Login successful!');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('Login failed!');
    console.error('Error status:', error.response?.status);
    console.error('Error message:', error.response?.data?.message || error.message);
    
    throw error;
  }
}

// Run the test
testAdminLogin()
  .then(data => {
    console.log('Test completed successfully!');
  })
  .catch(error => {
    console.error('Test failed!');
  });
