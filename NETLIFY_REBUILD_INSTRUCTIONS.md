# Rebuilding Your Netlify Site

Follow these instructions to rebuild your Netlify site with the updated environment configuration.

## Current Site Information

- **Site URL**: https://magenta-semolina-ed3ed4.netlify.app/
- **Backend API**: https://pixel-prints-1.onrender.com

## Rebuild Options

### Option 1: Automatic Rebuild via GitHub Integration

If your Netlify site is already connected to your GitHub repository, it should automatically rebuild when you push changes to the master branch. You can verify this by:

1. Go to the [Netlify Dashboard](https://app.netlify.com/)
2. Select your site (magenta-semolina-ed3ed4)
3. Go to the "Deploys" tab
4. Check if a new deployment has been triggered

### Option 2: Manual Rebuild

If automatic deployment is not set up, you can manually trigger a rebuild:

1. Go to the [Netlify Dashboard](https://app.netlify.com/)
2. Select your site (magenta-semolina-ed3ed4)
3. Go to the "Deploys" tab
4. Click on "Trigger deploy" and select "Deploy site"

### Option 3: Update Environment Variables

Make sure your environment variables are correctly set:

1. Go to the [Netlify Dashboard](https://app.netlify.com/)
2. Select your site (magenta-semolina-ed3ed4)
3. Go to "Site settings" > "Environment variables"
4. Verify that `REACT_APP_API_URL` is set to `https://pixel-prints-1.onrender.com`
5. Add any missing environment variables from the `netlify.toml` file
6. After updating environment variables, trigger a new deployment

## Verifying the Update

After rebuilding, verify that your frontend is correctly connected to the backend:

1. Open your site in a browser: https://magenta-semolina-ed3ed4.netlify.app/
2. Open the browser developer tools (F12)
3. Go to the Network tab
4. Look for API requests to `https://pixel-prints-1.onrender.com`
5. Verify that the requests are successful (status 200)

If you encounter any CORS errors, make sure your backend's `FRONTEND_URL` environment variable includes your Netlify domain.
