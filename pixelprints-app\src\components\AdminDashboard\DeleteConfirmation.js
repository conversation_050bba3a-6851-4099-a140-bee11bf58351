import React from 'react';
import './DeleteConfirmation.css';

const DeleteConfirmation = ({ user, onConfirm, onCancel, isDeleting }) => {
  return (
    <div className="delete-confirmation">
      <div className="delete-confirmation-content">
        <h2>Confirm Deletion</h2>
        <p>
          Are you sure you want to delete the user <strong>{user.name}</strong> ({user.email})?
        </p>
        <p className="warning">This action cannot be undone.</p>
        
        <div className="confirmation-actions">
          <button 
            className="cancel-button" 
            onClick={onCancel}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button 
            className="delete-button" 
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete User'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmation;
