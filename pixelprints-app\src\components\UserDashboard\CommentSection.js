import React, { useState, useEffect } from 'react';
import { getApiUrl } from '../../utils/api';
import './CommentSection.css';

const CommentSection = ({ orderId, userName }) => {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Fetch comments when component mounts
  useEffect(() => {
    if (orderId) {
      fetchComments();
    } else {
      setError('Order ID is required to load comments');
    }
  }, [orderId]);

  const fetchComments = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch(getApiUrl(`/api/orders/${orderId}/comments`));

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to fetch comments');
      }

      const data = await response.json();
      setComments(data);
    } catch (error) {
      console.error('Error fetching comments:', error);
      setError('Failed to load comments. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitComment = async (e) => {
    e.preventDefault();

    if (!newComment.trim()) {
      return;
    }

    if (!orderId) {
      setError('Order ID is required to add a comment');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch(getApiUrl(`/api/orders/${orderId}/comments`), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: newComment,
          author: userName || 'Customer'
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add comment');
      }

      // Add new comment to the list
      setComments(prevComments => [...prevComments, data.comment]);
      setNewComment(''); // Clear input
    } catch (error) {
      console.error('Error adding comment:', error);
      setError(error.message || 'Failed to add comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleString(undefined, options);
  };

  return (
    <div className="comment-section">
      <div className="comment-section-header">
        <h3>Order Comments</h3>
        <button
          type="button"
          className="refresh-button"
          onClick={fetchComments}
          disabled={isLoading}
        >
          {isLoading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          <p>{error}</p>
          <button type="button" onClick={fetchComments}>Try Again</button>
        </div>
      )}

      <div className="comments-list">
        {isLoading ? (
          <p className="loading-message">Loading comments...</p>
        ) : comments.length === 0 ? (
          <p className="no-comments-message">No comments yet. Be the first to add a comment!</p>
        ) : (
          comments.map(comment => (
            <div key={comment.id} className="comment">
              <div className="comment-header">
                <span className="comment-author">{comment.author}</span>
                <span className="comment-date">{formatDate(comment.timestamp)}</span>
              </div>
              <p className="comment-text">{comment.text}</p>
            </div>
          ))
        )}
      </div>

      <form onSubmit={handleSubmitComment} className="comment-form">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment about your order..."
          rows={3}
          disabled={isSubmitting}
          required
        />
        <button
          type="submit"
          className="submit-button"
          disabled={isSubmitting || !newComment.trim()}
        >
          {isSubmitting ? 'Posting...' : 'Post Comment'}
        </button>
      </form>
    </div>
  );
};

export default CommentSection;
