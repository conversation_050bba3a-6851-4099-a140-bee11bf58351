const db = require('../db');
require('dotenv').config();

async function updateUsersTable() {
  try {
    // Add reset token fields to users table
    await db.query(`
      ALTER TABLE users
      ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255),
      ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP;
    `);

    console.log('Users table updated successfully with reset token fields');
    process.exit(0);
  } catch (error) {
    console.error('Error updating users table:', error);
    process.exit(1);
  }
}

updateUsersTable();
