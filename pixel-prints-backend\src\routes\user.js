const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { 
  getUserOrders, 
  getOrderById,
  createOrder 
} = require('../controllers/orders');
const {
  getUserProfile,
  updateUserProfile
} = require('../controllers/users');

// Get user profile
router.get('/profile', authenticateToken, getUserProfile);

// Update user profile
router.put('/profile', authenticateToken, updateUserProfile);

// Get all orders for the authenticated user
router.get('/orders', authenticateToken, getUserOrders);

// Get a specific order by ID
router.get('/orders/:id', authenticateToken, getOrderById);

// Create a new order
router.post('/orders', authenticateToken, createOrder);

module.exports = router;