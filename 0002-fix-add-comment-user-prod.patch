From 1bbf18cd8dbd59d02e0d1a599c7f55b2cb5594fd Mon Sep 17 00:00:00 2001
From: git-teng-test <<EMAIL>>
Date: Tue, 22 Apr 2025 18:38:38 +0800
Subject: [PATCH 2/2] fix add comment user prod

---
 pixel-prints-backend/ensure-admin.js | 62 ++++++++++++++++++++++++++++
 1 file changed, 62 insertions(+)
 create mode 100644 pixel-prints-backend/ensure-admin.js

diff --git a/pixel-prints-backend/ensure-admin.js b/pixel-prints-backend/ensure-admin.js
new file mode 100644
index 0000000..42f7da6
--- /dev/null
+++ b/pixel-prints-backend/ensure-admin.js
@@ -0,0 +1,62 @@
+const bcrypt = require('bcryptjs');
+const db = require('./db');
+require('dotenv').config();
+
+async function ensureAdminExists() {
+  try {
+    console.log('Checking for admin user...');
+    
+    // Check if admin user exists
+    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
+    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
+    
+    console.log(`Looking for admin with email: ${adminEmail}`);
+    
+    const result = await db.query('SELECT * FROM users WHERE email = $1 AND role = $2', [adminEmail, 'admin']);
+    
+    if (result.rows.length === 0) {
+      console.log('No admin user found. Creating admin user...');
+      
+      // Hash password
+      const salt = await bcrypt.genSalt(10);
+      const hashedPassword = await bcrypt.hash(adminPassword, salt);
+      
+      // Create admin user
+      const insertResult = await db.query(
+        'INSERT INTO users (name, email, password, role) VALUES ($1, $2, $3, $4) RETURNING id, name, email, role',
+        ['Admin User', adminEmail, hashedPassword, 'admin']
+      );
+      
+      console.log('Admin user created successfully:', insertResult.rows[0]);
+    } else {
+      console.log('Admin user already exists:', result.rows[0].email);
+      
+      // Update admin password if needed
+      const admin = result.rows[0];
+      const isPasswordValid = admin.password ? await bcrypt.compare(adminPassword, admin.password) : false;
+      
+      if (!isPasswordValid) {
+        console.log('Updating admin password...');
+        
+        // Hash password
+        const salt = await bcrypt.genSalt(10);
+        const hashedPassword = await bcrypt.hash(adminPassword, salt);
+        
+        // Update admin password
+        await db.query('UPDATE users SET password = $1 WHERE id = $2', [hashedPassword, admin.id]);
+        
+        console.log('Admin password updated successfully');
+      }
+    }
+    
+    console.log('Admin user check completed');
+  } catch (error) {
+    console.error('Error ensuring admin exists:', error);
+  } finally {
+    // Close the database connection
+    await db.end();
+  }
+}
+
+// Run the function
+ensureAdminExists();
-- 
2.47.0.windows.1

