# Pixel Prints Application

## Overview

Pixel Prints is a professional document and photo printing service with delivery across Luzon, Philippines. This repository contains both the frontend and backend code for the application.

## Deployment

- **Frontend**: Deployed on Netlify at [https://magenta-semolina-ed3ed4.netlify.app/](https://magenta-semolina-ed3ed4.netlify.app/)
- **Backend**: Deployed on Render at [https://pixel-prints-1.onrender.com](https://pixel-prints-1.onrender.com)
- **Database**: PostgreSQL database hosted on Render

## Components

### Frontend (`pixelprints-app`)

- A React-based web application where customers can place print orders
- Features a form to collect customer details, print specifications, and file uploads
- Includes a server status checker
- Uses CSS for styling
- Responsive design for mobile and desktop users

### Backend (`pixel-prints-backend`)

- Node.js/Express server handling order processing
- Implements file upload functionality using Multer
- Sends order notifications via email using Nodemailer
- Includes health check endpoints
- Handles CORS for frontend communication
- Runs on port 3001 by default
- PostgreSQL database for storing orders and user information

## Service Areas

The application is focused on serving the NCR and Bulacan areas in the Philippines, with delivery options available across Luzon. Orders are processed through email notifications sent to `<EMAIL>`.

## Last Updated

April 22, 2025 - Updated environment configuration for frontend and backend
