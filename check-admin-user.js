const { Pool } = require('pg');
require('dotenv').config();

// Try different database configurations
let pool;

// Try with the credentials from the config file first
try {
  console.log('Trying with config file credentials...');
  const dbConfig = require('./pixel-prints-backend/config/db.config.js');
  pool = new Pool({
    user: dbConfig.USER,
    host: dbConfig.HOST,
    database: dbConfig.DB,
    password: dbConfig.PASSWORD,
    port: dbConfig.port
  });
} catch (error) {
  console.error('Error loading db config:', error);
  console.log('Trying with default values');

  // Try with default values
  try {
    pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: 'pixel_prints',
      password: 'postgres',
      port: 5432
    });
  } catch (error) {
    console.error('Error with default values:', error);

    // Try with DATABASE_URL
    if (process.env.DATABASE_URL) {
      console.log('Trying with DATABASE_URL...');
      pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: false
      });
    } else {
      console.error('No valid database configuration found');
      process.exit(1);
    }
  }
}

async function checkAdminUser() {
  try {
    console.log('Attempting to connect to database...');
    const client = await pool.connect();
    console.log('Database connection successful!');

    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      );
    `);

    if (tableCheck.rows[0].exists) {
      console.log('Users table exists.');

      // Check for admin user
      const adminCheck = await client.query(`
        SELECT id, name, email, role FROM users WHERE email = '<EMAIL>';
      `);

      if (adminCheck.rows.length > 0) {
        console.log('Admin user found:', adminCheck.rows[0]);
      } else {
        console.log('Admin user not found!');

        // Create admin user
        console.log('Creating admin user...');
        const bcrypt = require('bcryptjs');
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash('admin123', salt);

        await client.query(`
          INSERT INTO users (name, email, password, role)
          VALUES ('Admin', '<EMAIL>', $1, 'admin');
        `, [hashedPassword]);

        console.log('Admin user created successfully.');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123');
      }
    } else {
      console.log('Users table does not exist!');

      // Create users table
      console.log('Creating users table...');
      await client.query(`
        CREATE TABLE users (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          email VARCHAR(100) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          role VARCHAR(20) DEFAULT 'user',
          reset_token VARCHAR(255),
          reset_token_expires TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);

      console.log('Users table created successfully.');

      // Create admin user
      console.log('Creating admin user...');
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);

      await client.query(`
        INSERT INTO users (name, email, password, role)
        VALUES ('Admin', '<EMAIL>', $1, 'admin');
      `, [hashedPassword]);

      console.log('Admin user created successfully.');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
    }

    client.release();
  } catch (error) {
    console.error('Database operation error:', error);
  } finally {
    await pool.end();
  }
}

checkAdminUser();
