<<<<<<< HEAD
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import OrderCard from '../../components/dashboard/user/OrderCard';
import UserStats from '../../components/dashboard/user/UserStats';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
=======
import { useEffect } from 'react'
import { useRouter } from 'next/router'
import { UserDashboard } from '@/components/dashboard'
>>>>>>> 547cd6e7b3b43c148453f821de4c72fbb8a35a8c

export default function DashboardPage() {
  const router = useRouter()
  const userEmail = localStorage.getItem('userEmail')
  const userName = localStorage.getItem('userName')

  useEffect(() => {
<<<<<<< HEAD
    const fetchOrders = async () => {
      try {
        // Mock data since getOrders() is not available
        const mockOrders = [
          { id: 1, order_number: 'ORD-001', status: 'pending', price: '100.00', created_at: new Date().toISOString() },
          { id: 2, order_number: 'ORD-002', status: 'completed', price: '200.00', created_at: new Date().toISOString() }
        ];
        setOrders(mockOrders);

        // Calculate stats
        const totalOrders = mockOrders.length;
        const pendingOrders = mockOrders.filter(order => order.status === 'pending').length;
        const completedOrders = mockOrders.filter(order => order.status === 'completed').length;
        const totalSpent = mockOrders.reduce((sum, order) => sum + parseFloat(order.price || 0), 0);

        setStats({
          totalOrders,
          pendingOrders,
          completedOrders,
          totalSpent
        });

        setLoading(false);
      } catch (error) {
        console.error('Error fetching orders:', error);
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">My Dashboard</h1>

      <UserStats stats={stats} />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2">
              <Link to="/new-order" className="bg-primary text-white py-2 px-4 rounded text-center hover:bg-primary/90">
                Place New Order
              </Link>
              <Link to="/user/orders" className="bg-secondary text-white py-2 px-4 rounded text-center hover:bg-secondary/90">
                View All Orders
              </Link>
              <Link to="/user/profile" className="bg-muted text-primary py-2 px-4 rounded text-center hover:bg-muted/90">
                Edit Profile
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Account Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2"><strong>Name:</strong> {localStorage.getItem('userName') || 'User'}</p>
            <p className="mb-2"><strong>Email:</strong> {localStorage.getItem('userEmail') || '<EMAIL>'}</p>
            <p className="mb-2"><strong>Member Since:</strong> {localStorage.getItem('memberSince') || 'January 2023'}</p>
            <p><strong>Last Order:</strong> {orders.length > 0 ? new Date(orders[0].createdAt).toLocaleDateString() : 'No orders yet'}</p>
          </CardContent>
        </Card>
      </div>

      <h2 className="text-xl font-semibold mb-4">Recent Orders</h2>
      {loading ? (
        <p>Loading orders...</p>
      ) : orders.length > 0 ? (
        <div className="grid grid-cols-1 gap-4">
          {orders.slice(0, 5).map(order => (
            <OrderCard key={order.id} order={order} />
          ))}
          {orders.length > 5 && (
            <div className="text-center mt-4">
              <Link to="/user/orders" className="text-primary hover:underline">
                View all {orders.length} orders
              </Link>
            </div>
          )}
        </div>
      ) : (
        <p>You haven't placed any orders yet.</p>
      )}
    </div>
  );
};
=======
    if (!userEmail) {
      router.push('/login')
    }
  }, [userEmail, router])

  if (!userEmail) {
    return <div className="text-center p-4">Redirecting to login...</div>
  }
>>>>>>> 547cd6e7b3b43c148453f821de4c72fbb8a35a8c

  return <UserDashboard userEmail={userEmail} userName={userName} />
}
