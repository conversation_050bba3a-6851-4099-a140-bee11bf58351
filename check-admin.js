const bcrypt = require('bcryptjs');
const db = require('./db');

async function checkAndRecreateAdmin() {
  try {
    console.log('Checking for admin user...');
    
    // Check if users table exists
    const tableCheck = await db.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'users'
      );
    `);
    
    if (!tableCheck.rows[0].exists) {
      console.log('Users table does not exist. Creating it...');
      await db.query(`
        CREATE TABLE users (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          email VARCHAR(100) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          role VARCHAR(20) DEFAULT 'user',
          reset_token VARCHAR(255),
          reset_token_expires TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('Users table created successfully.');
    }
    
    // Check if admin user exists
    const adminCheck = await db.query(`
      SELECT * FROM users WHERE email = '<EMAIL>';
    `);
    
    if (adminCheck.rows.length > 0) {
      console.log('Admin user exists. Recreating with new password...');
      
      // Delete existing admin user
      await db.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
    } else {
      console.log('Admin user does not exist. Creating new admin user...');
    }
    
    // Create admin user with password 'admin123'
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    await db.query(`
      INSERT INTO users (name, email, password, role)
      VALUES ('Admin', '<EMAIL>', $1, 'admin');
    `, [hashedPassword]);
    
    console.log('Admin user created successfully.');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
    // Verify the admin user was created
    const verifyAdmin = await db.query(`
      SELECT id, name, email, role FROM users WHERE email = '<EMAIL>';
    `);
    
    if (verifyAdmin.rows.length > 0) {
      console.log('Admin user verified:', verifyAdmin.rows[0]);
    } else {
      console.log('Warning: Admin user could not be verified after creation!');
    }
    
  } catch (error) {
    console.error('Error checking/recreating admin user:', error);
  } finally {
    // Close the database connection
    if (db.end) {
      await db.end();
    }
    console.log('Database connection closed.');
  }
}

checkAndRecreateAdmin();
