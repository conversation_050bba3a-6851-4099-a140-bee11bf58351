const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function testConnection() {
  try {
    // Test basic connection
    const client = await pool.connect();
    console.log('Database connection successful');

    // Test query execution
    const result = await client.query('SELECT COUNT(*) FROM orders');
    console.log('Total orders in database:', result.rows[0].count);

    // Test specific customer query
    const customerEmail = '<EMAIL>';
    const orderResult = await client.query(
      'SELECT * FROM orders WHERE customer_email = $1',
      [customerEmail]
    );
    console.log(`Orders for ${customerEmail}:`, orderResult.rows);

    client.release();
  } catch (err) {
    console.error('Database connection error:', err);
  } finally {
    await pool.end();
  }
}

testConnection();