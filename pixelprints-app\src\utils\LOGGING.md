# Secure Logging System for Pixel Prints

This document explains the secure logging system implemented in the Pixel Prints application to prevent sensitive information from being exposed in browser console logs.

## Overview

The logging system consists of several components:

1. **Custom Logger (`logger.js`)**: A utility that filters and masks sensitive information in logs
2. **Console Override (`consoleOverride.js`)**: Overrides the global console object to ensure all logs are filtered
3. **API Integration (`api.js`)**: Uses the custom logger for API-related errors

## How It Works

### In Development Mode

- All console logs are displayed normally to facilitate debugging
- No information is masked or filtered

### In Production Mode

- Only error logs are displayed by default (configurable)
- Sensitive information is automatically masked
- Informational, debug, and warning logs are suppressed

## Configuration

The logging behavior can be configured in `logger.js` by modifying the `PRODUCTION_CONFIG` object:

```javascript
const PRODUCTION_CONFIG = {
  // Allow error logs in production for debugging critical issues
  allowErrors: true,
  // Don't allow informational logs in production
  allowInfo: false,
  // Don't allow debug logs in production
  allowDebug: false,
  // Don't allow warning logs in production
  allowWarnings: false,
  // Mask sensitive data in logs (passwords, tokens, etc.)
  maskSensitiveData: true
};
```

## Usage

### Recommended Usage

Use the custom logger directly:

```javascript
import logger from './utils/logger';

// Log an error (will be displayed in both development and production)
logger.error('Error fetching data:', error);

// Log info (only displayed in development by default)
logger.info('User logged in:', username);

// Log debug information (only displayed in development by default)
logger.debug('API response:', data);

// Log a warning (only displayed in development by default)
logger.warn('Deprecated function used');
```

### Direct Console Usage

Even if you use the console directly, the override will ensure sensitive data is protected in production:

```javascript
// These will be automatically filtered in production
console.log('User data:', userData);
console.error('Authentication error:', error);
```

## Sensitive Information Detection

The system automatically detects and masks information that might be sensitive based on:

1. **Property names**: Properties like `password`, `token`, `secret`, etc.
2. **Content analysis**: Strings that contain keywords indicating sensitive information
3. **Object structure**: Recursively checks nested objects and arrays

## Extending the System

To add more sensitive keywords or property names, modify the arrays in `logger.js`:

```javascript
// List of keywords that might indicate sensitive information
const sensitiveKeywords = [
  'password', 'token', 'secret', 'key', 'auth', 'credential', 'admin',
  'credit', 'card', 'cvv', 'expiry', 'ssn', 'social', 'security',
  'license', 'address', 'phone', 'email', 'dob', 'birth'
  // Add more keywords here
];

// List of property names that typically contain sensitive information
const sensitiveProps = [
  'password', 'token', 'secret', 'key', 'auth', 'credential',
  'creditCard', 'cvv', 'expiry', 'ssn', 'socialSecurity',
  'licenseNumber', 'address', 'phoneNumber', 'email', 'dob', 'dateOfBirth'
  // Add more property names here
];
```

## Restoring Original Console Behavior

If needed, you can restore the original console behavior:

```javascript
import { restoreConsole } from './utils/consoleOverride';

// Restore original console behavior
restoreConsole();
```
