import { useState } from 'react';

const ImageUploader = ({ onImageUpload }) => {
  const [errorMessage, setErrorMessage] = useState('');

  // Handle file selection
  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
    if (!validTypes.includes(file.type)) {
      setErrorMessage('Please upload a valid image file (JPEG, PNG, GIF, SVG)');
      return;
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      setErrorMessage('Image size should be less than 5MB');
      return;
    }

    // Clear any previous errors
    setErrorMessage('');

    // Create a FileReader to read the file
    const reader = new FileReader();

    // Set up the FileReader onload event
    reader.onload = (event) => {
      // Call the parent component's callback with the image data URL
      onImageUpload(event.target.result);
    };

    // Read the file as a data URL
    reader.readAsDataURL(file);
  };

  return (
    <div className="image-uploader">
      <h3>Add Images</h3>

      <div className="upload-container">
        <label className="file-input-label">
          Choose Image
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="file-input"
          />
        </label>

        <p className="upload-instructions">
          Click the button above to select an image from your device
        </p>
      </div>

      {errorMessage && (
        <div className="error-message">
          {errorMessage}
        </div>
      )}

      <div className="image-tips">
        <p>Supported formats: JPEG, PNG, GIF, SVG</p>
        <p>Max file size: 5MB</p>
      </div>
    </div>
  );
};

export default ImageUploader;
