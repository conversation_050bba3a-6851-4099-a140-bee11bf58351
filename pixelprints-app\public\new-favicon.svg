<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="32" fill="#ffffff"/>
  
  <!-- Printer design based on the provided image -->
  <g transform="translate(12, 12)">
    <!-- Printer top (gray) -->
    <rect x="2" y="2" width="36" height="6" fill="#c0c0c0" rx="1" />
    
    <!-- Printer body (blue) -->
    <rect x="2" y="8" width="36" height="18" fill="#4a90e2" rx="2" />
    
    <!-- Printer output tray (dark gray) -->
    <rect x="6" y="26" width="28" height="4" fill="#333333" rx="1" />
    
    <!-- Printer button (white) -->
    <circle cx="32" cy="14" r="1.5" fill="#ffffff" />
  </g>
  
  <!-- Text for PP -->
  <text x="32" y="48" text-anchor="middle" font-family="Arial, sans-serif" 
        font-weight="bold" font-size="12" fill="#333333">PP</text>
</svg>
