import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import ResetPassword from './ResetPassword';

// Mock the useNavigate and useParams hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
  useParams: () => ({ token: 'test-token' }),
}));

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ message: 'Your password has been reset successfully.' }),
  })
);

describe('ResetPassword Component', () => {
  beforeEach(() => {
    // Reset mock function calls before each test
    fetch.mockClear();
  });

  test('renders reset password form', () => {
    render(
      <BrowserRouter>
        <ResetPassword />
      </BrowserRouter>
    );
    
    // Check if form elements are present
    expect(screen.getByText('Reset Password')).toBeInTheDocument();
    expect(screen.getByText('Create a new password')).toBeInTheDocument();
    expect(screen.getByLabelText('New Password')).toBeInTheDocument();
    expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument();
    expect(screen.getByText('Reset Password')).toBeInTheDocument();
    expect(screen.getByText('Back to Home')).toBeInTheDocument();
  });

  test('validates password match', async () => {
    render(
      <BrowserRouter>
        <ResetPassword />
      </BrowserRouter>
    );
    
    // Enter mismatched passwords and submit form
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: 'password123' } });
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password456' } });
    fireEvent.click(screen.getByText('Reset Password'));
    
    // Check if validation error is displayed
    expect(screen.getByText('Passwords do not match.')).toBeInTheDocument();
    
    // Verify that fetch was not called
    expect(fetch).not.toHaveBeenCalled();
  });

  test('validates password length', async () => {
    render(
      <BrowserRouter>
        <ResetPassword />
      </BrowserRouter>
    );
    
    // Enter short password and submit form
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: 'pass' } });
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'pass' } });
    fireEvent.click(screen.getByText('Reset Password'));
    
    // Check if validation error is displayed
    expect(screen.getByText('Password must be at least 6 characters long.')).toBeInTheDocument();
    
    // Verify that fetch was not called
    expect(fetch).not.toHaveBeenCalled();
  });

  test('submits form with valid passwords', async () => {
    render(
      <BrowserRouter>
        <ResetPassword />
      </BrowserRouter>
    );
    
    // Enter valid passwords and submit form
    fireEvent.change(screen.getByLabelText('New Password'), { target: { value: 'password123' } });
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } });
    fireEvent.click(screen.getByText('Reset Password'));
    
    // Verify that fetch was called with correct arguments
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/reset-password'),
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({ token: 'test-token', password: 'password123' }),
      })
    );
  });
});
