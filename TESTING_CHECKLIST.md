# PixelPrints App Testing Checklist

This document provides a comprehensive testing checklist for the PixelPrints application, with a focus on the authentication and forgot password features.

## Prerequisites

1. Ensure the backend server is running:
   ```
   cd pixel-prints-backend
   npm start
   ```

2. Ensure the frontend application is running:
   ```
   cd pixelprints-app
   npm start
   ```

3. Make sure you have a valid email configuration in the `.env` file for testing the forgot password email functionality.

## Authentication Testing

### Login Functionality

- [ ] Navigate to the application homepage
- [ ] Click on "Sign Up / Login" button
- [ ] Verify the login form appears with the following elements:
  - [ ] "Welcome Back" header
  - [ ] Email input field
  - [ ] Password input field
  - [ ] "Forgot Password?" link
  - [ ] "Sign In" button
  - [ ] "Don't have an account? Sign Up" text and link
- [ ] Test login with invalid credentials (should show error message)
- [ ] Test login with valid credentials (should log in successfully)
- [ ] Verify user is redirected to the dashboard after successful login

### Registration Functionality

- [ ] Navigate to the login form
- [ ] Click on "Sign Up" link
- [ ] Verify the registration form appears with the following elements:
  - [ ] "Create Account" header
  - [ ] Name input field
  - [ ] Email input field
  - [ ] Password input field
  - [ ] "Create Account" button
  - [ ] "Already have an account? Sign In" text and link
- [ ] Test registration with invalid email format (should show error message)
- [ ] Test registration with missing name (should show error message)
- [ ] Test registration with valid information (should register successfully)
- [ ] Verify user is redirected to the dashboard after successful registration

## Forgot Password Testing

### Request Password Reset

- [ ] Navigate to the login form
- [ ] Click on "Forgot Password?" link
- [ ] Verify the forgot password form appears with the following elements:
  - [ ] "Forgot Password" header
  - [ ] Email input field
  - [ ] "Send Reset Link" button
  - [ ] "Back to Home" link
- [ ] Test with invalid email format (should show error message)
- [ ] Test with valid email that doesn't exist in the database (should show success message for security)
- [ ] Test with valid email that exists in the database (should show success message)
- [ ] Verify that a password reset email is sent to the valid email address

### Reset Password

- [ ] Open the password reset link from the email
- [ ] Verify the reset password form appears with the following elements:
  - [ ] "Reset Password" header
  - [ ] New Password input field
  - [ ] Confirm Password input field
  - [ ] "Reset Password" button
  - [ ] "Back to Home" link
- [ ] Test with passwords that don't match (should show error message)
- [ ] Test with password shorter than 6 characters (should show error message)
- [ ] Test with valid matching passwords (should reset password successfully)
- [ ] Verify success message appears after password reset
- [ ] Navigate to login form and verify you can log in with the new password

## Visual and UI Testing

- [ ] Verify that all forms are properly styled and aligned
- [ ] Verify that error messages are clearly visible and properly styled
- [ ] Verify that success messages are clearly visible and properly styled
- [ ] Verify that buttons have proper hover and active states
- [ ] Verify that the forms are responsive and look good on different screen sizes
- [ ] Verify that the "Place Order" button and other UI elements are not affected by the auth component's CSS

## Security Testing

- [ ] Verify that passwords are not sent in plain text (check network requests)
- [ ] Verify that reset tokens expire after the specified time (1 hour)
- [ ] Verify that reset tokens can only be used once
- [ ] Verify that the system doesn't reveal whether an email exists in the database during password reset

## Edge Cases

- [ ] Test with very long email addresses
- [ ] Test with very long passwords
- [ ] Test with special characters in email and password
- [ ] Test with expired reset tokens
- [ ] Test with invalid reset tokens
- [ ] Test with multiple password reset requests for the same email

## Notes

- For security reasons, the system always returns a success message when requesting a password reset, regardless of whether the email exists in the database.
- Reset tokens expire after 1 hour.
- The reset password link will only work on the machine where the application is running in development mode. For production, set the `FRONTEND_URL` environment variable to your actual domain.
