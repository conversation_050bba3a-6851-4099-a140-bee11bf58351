import React, { useState, useEffect } from 'react';
import './AdminDashboard.css';
import OrderList from './OrderList';
import UserList from './UserList';
import DailyLimitSettings from './DailyLimitSettings';
import SiteNotificationSettings from './SiteNotificationSettings';
import AdminStats from './AdminStats';
import AssistedLocations from './AssistedLocations';
import { getApiUrl } from '../../utils/api';

const AdminDashboard = ({ onBackToMain }) => {
  const [activeTab, setActiveTab] = useState('orders');
  const [orders, setOrders] = useState([]);
  const [users, setUsers] = useState([]);
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    totalRevenue: 0,
    ordersToday: 0
  });

  // Fetch orders
  const fetchOrders = async () => {
    try {
      const response = await fetch(getApiUrl('/api/orders'));
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      const data = await response.json();
      setOrders(data);

      // Calculate stats
      const totalOrders = data.length;
      const pendingOrders = data.filter(order => order.status === 'pending').length;
      const completedOrders = data.filter(order => order.status === 'completed').length;
      const totalRevenue = data.reduce((sum, order) => sum + parseFloat(order.price || 0), 0);

      // Count orders from today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const ordersToday = data.filter(order => new Date(order.created_at) >= today).length;

      setStats({
        totalOrders,
        pendingOrders,
        completedOrders,
        totalRevenue,
        ordersToday
      });
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to fetch orders. Please try again later.');
    }
  };

  // Helper function to scan localStorage for all user data
  const scanLocalStorageForUsers = () => {
    const users = {};

    // Check for current user
    const storedUserEmail = localStorage.getItem('userEmail');
    const storedUserName = localStorage.getItem('userName');
    const storedIsAuthenticated = localStorage.getItem('isAuthenticated');

    if (storedIsAuthenticated === 'true' && storedUserEmail) {
      users[storedUserEmail] = {
        id: `u-${Object.keys(users).length + 1}`,
        name: storedUserName || 'User',
        email: storedUserEmail,
        role: 'user',
        created_at: new Date().toISOString()
      };
    }

    // Scan all localStorage items
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);

      // Look for email keys that might indicate user data
      if (key && (key.includes('email') || key.includes('Email'))) {
        const email = localStorage.getItem(key);
        if (email && !users[email] && email.includes('@')) {
          // Try to find a corresponding name
          let name = 'User';
          const possibleNameKeys = [
            key.replace('email', 'name'),
            key.replace('Email', 'Name'),
            'userName'
          ];

          for (const nameKey of possibleNameKeys) {
            const foundName = localStorage.getItem(nameKey);
            if (foundName) {
              name = foundName;
              break;
            }
          }

          users[email] = {
            id: `u-${Object.keys(users).length + 1}`,
            name: name,
            email: email,
            role: 'user',
            created_at: new Date().toISOString()
          };
        }
      }
    }

    return Object.values(users);
  };

  // Fetch users
  const fetchUsers = async () => {
    try {
      // Fetch users from the database
      const response = await fetch(getApiUrl('/api/users/admin-dashboard'));
      if (!response.ok) {
        throw new Error('Failed to fetch users from database');
      }
      const dbUsers = await response.json();

      // Create a map of users by email for easy lookup
      const uniqueUsers = {};

      // Add users from database
      dbUsers.forEach(user => {
        // Ensure we have a valid email to use as a key
        if (!user.email) return;

        // Format the ID as a string if it's a number
        let formattedId = user.id;
        if (typeof user.id === 'number') {
          formattedId = String(user.id);
        } else if (!user.id) {
          formattedId = `db-${Object.keys(uniqueUsers).length + 1}`;
        }

        uniqueUsers[user.email] = {
          id: formattedId,
          name: user.name || 'Unknown',
          email: user.email,
          role: user.role || 'user',
          created_at: user.created_at || new Date().toISOString()
        };
      });

      // Make sure admin user is included
      if (!uniqueUsers['<EMAIL>']) {
        uniqueUsers['<EMAIL>'] = {
          id: 'admin-1',
          name: 'Admin',
          email: '<EMAIL>',
          role: 'admin',
          created_at: new Date().toISOString()
        };
      }

      // Get users from localStorage that might not be in the database yet
      const localStorageUsers = scanLocalStorageForUsers();

      // Add localStorage users to uniqueUsers if they're not already there
      localStorageUsers.forEach(user => {
        if (!uniqueUsers[user.email]) {
          uniqueUsers[user.email] = user;
        }
      });

      // Convert the map to an array and sort by role (admin first, then users, then customers)
      const sortedUsers = Object.values(uniqueUsers).sort((a, b) => {
        // Sort by role priority
        const rolePriority = { 'admin': 1, 'user': 2, 'customer': 3 };
        const priorityA = rolePriority[a.role] || 4;
        const priorityB = rolePriority[b.role] || 4;

        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }

        // If same role, sort by created_at (newest first)
        return new Date(b.created_at || 0) - new Date(a.created_at || 0);
      });

      setUsers(sortedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      // Don't set error state here to avoid blocking the entire dashboard
      // Just log the error and continue with empty users array

      // Fallback to localStorage users if database fetch fails
      const localStorageUsers = scanLocalStorageForUsers();
      setUsers(localStorageUsers);
    }
  };

  // Fetch settings
  const fetchSettings = async () => {
    try {
      const response = await fetch(getApiUrl('/api/settings'));
      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }
      const data = await response.json();

      // Convert settings array to object for easier access
      const settingsObj = {};
      data.forEach(setting => {
        settingsObj[setting.key] = setting.value;
      });

      setSettings(settingsObj);
    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to fetch settings. Please try again later.');
    }
  };

  // Manual refresh of users list
  const refreshUsers = async () => {
    try {
      // Show loading state if needed
      setLoading(true);

      // Fetch users
      await fetchUsers();

      // Hide loading state
      setLoading(false);
    } catch (error) {
      console.error('Error refreshing users:', error);
      setLoading(false);
    }
  };

  // Fetch all data
  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchOrders(),
        fetchUsers(),
        fetchSettings()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to fetch data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const response = await fetch(getApiUrl(`/api/orders/${orderId}/status`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order status');
      }

      // Refresh orders after update
      fetchOrders();
    } catch (error) {
      console.error('Error updating order status:', error);
      setError('Failed to update order status. Please try again later.');
    }
  };

  // Delete order
  const deleteOrder = async (orderId) => {
    try {
      const response = await fetch(getApiUrl(`/api/orders/${orderId}`), {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete order');
      }

      // Refresh orders after deletion
      fetchOrders();
    } catch (error) {
      console.error('Error deleting order:', error);
      setError('Failed to delete order. Please try again later.');
    }
  };

  // Update daily order limit
  const updateDailyOrderLimit = async (limit, enabled) => {
    try {
      // Update limit
      const limitResponse = await fetch(getApiUrl('/api/settings/daily_order_limit'), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: limit.toString(),
          description: 'Maximum number of orders allowed per day'
        }),
      });

      if (!limitResponse.ok) {
        throw new Error('Failed to update daily order limit');
      }

      // Update enabled status
      const enabledResponse = await fetch(getApiUrl('/api/settings/daily_order_limit_enabled'), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: enabled.toString(),
          description: 'Whether the daily order limit is enabled'
        }),
      });

      if (!enabledResponse.ok) {
        throw new Error('Failed to update daily order limit enabled status');
      }

      // Refresh settings after update
      fetchSettings();
    } catch (error) {
      console.error('Error updating daily order limit:', error);
      setError('Failed to update daily order limit. Please try again later.');
    }
  };

  // Update site notification settings
  const updateSiteNotification = async (message, enabled) => {
    try {
      // Update message
      const messageResponse = await fetch(getApiUrl('/api/settings/site-notification-message'), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: message,
          description: 'Message to display as site notification'
        }),
      });

      if (!messageResponse.ok) {
        throw new Error('Failed to update site notification message');
      }

      // Update enabled status
      const enabledResponse = await fetch(getApiUrl('/api/settings/site-notification-enabled'), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: enabled.toString(),
          description: 'Whether the site notification is enabled'
        }),
      });

      if (!enabledResponse.ok) {
        throw new Error('Failed to update site notification enabled status');
      }

      // Refresh settings after update
      fetchSettings();
    } catch (error) {
      console.error('Error updating site notification:', error);
      setError('Failed to update site notification. Please try again later.');
      throw error;
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchData();

    // Refresh data every 5 minutes
    const intervalId = setInterval(fetchData, 5 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <h1>Admin Dashboard</h1>
        <button className="back-button" onClick={onBackToMain}>
          Back to Main
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {loading ? (
        <div className="loading">Loading dashboard data...</div>
      ) : (
        <>
          {/* Dashboard Stats */}
          <AdminStats stats={stats} />

          {/* Navigation Tabs */}
          <div className="admin-tabs">
            <button
              className={`tab-button ${activeTab === 'orders' ? 'active' : ''}`}
              onClick={() => setActiveTab('orders')}
            >
              Orders
            </button>
            <button
              className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
              onClick={() => setActiveTab('users')}
            >
              Users
            </button>
            <button
              className={`tab-button ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              Order Limits
            </button>
            <button
              className={`tab-button ${activeTab === 'notifications' ? 'active' : ''}`}
              onClick={() => setActiveTab('notifications')}
            >
              Site Notifications
            </button>
            <button
              className={`tab-button ${activeTab === 'locations' ? 'active' : ''}`}
              onClick={() => setActiveTab('locations')}
            >
              Assisted Locations
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'orders' && (
              <OrderList
                orders={orders}
                updateOrderStatus={updateOrderStatus}
                deleteOrder={deleteOrder}
              />
            )}

            {activeTab === 'users' && (
              <UserList users={users} onRefresh={refreshUsers} loading={loading} />
            )}

            {activeTab === 'settings' && (
              <DailyLimitSettings
                settings={settings}
                updateDailyOrderLimit={updateDailyOrderLimit}
                ordersToday={stats.ordersToday}
              />
            )}

            {activeTab === 'notifications' && (
              <SiteNotificationSettings
                settings={settings}
                updateSiteNotification={updateSiteNotification}
              />
            )}

            {activeTab === 'locations' && (
              <AssistedLocations />
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default AdminDashboard;
