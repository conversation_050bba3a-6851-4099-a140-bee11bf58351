const db = require('./db');

async function checkDatabase() {
  try {
    // List all tables in the database
    console.log('Listing all tables:');
    const tablesResult = await db.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);
    
    console.log('Tables in database:');
    tablesResult.rows.forEach(table => {
      console.log(`- ${table.table_name}`);
    });
    
    // For each table, show its structure and count of records
    for (const table of tablesResult.rows) {
      const tableName = table.table_name;
      
      // Get column information
      const columnsResult = await db.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = $1
        ORDER BY ordinal_position;
      `, [tableName]);
      
      console.log(`\nTable: ${tableName}`);
      console.log('Columns:');
      columnsResult.rows.forEach(column => {
        console.log(`  - ${column.column_name} (${column.data_type})`);
      });
      
      // Count records
      const countResult = await db.query(`SELECT COUNT(*) FROM ${tableName};`);
      console.log(`Total records: ${countResult.rows[0].count}`);
      
      // Show sample data (first 5 records)
      const sampleResult = await db.query(`SELECT * FROM ${tableName} LIMIT 5;`);
      console.log('Sample data:');
      console.log(sampleResult.rows);
    }
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    // Close the database connection
    await db.end();
  }
}

checkDatabase();
