import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Auth from './Auth';

// Mock the useNavigate hook
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

describe('Auth Component', () => {
  const mockOnAuthSuccess = jest.fn();

  beforeEach(() => {
    // Reset mock function calls before each test
    mockOnAuthSuccess.mockReset();
  });

  test('renders login form by default', () => {
    render(
      <BrowserRouter>
        <Auth onAuthSuccess={mockOnAuthSuccess} />
      </BrowserRouter>
    );
    
    // Check if login form elements are present
    expect(screen.getByText('Welcome Back')).toBeInTheDocument();
    expect(screen.getByText('Sign in to continue')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByText('Forgot Password?')).toBeInTheDocument();
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
    expect(screen.getByText('Sign Up')).toBeInTheDocument();
  });

  test('switches to register form when Sign Up is clicked', () => {
    render(
      <BrowserRouter>
        <Auth onAuthSuccess={mockOnAuthSuccess} />
      </BrowserRouter>
    );
    
    // Click the Sign Up button
    fireEvent.click(screen.getByText('Sign Up'));
    
    // Check if register form elements are present
    expect(screen.getByText('Create Account')).toBeInTheDocument();
    expect(screen.getByText('Register to get started')).toBeInTheDocument();
    expect(screen.getByLabelText('Full Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByText('Create Account')).toBeInTheDocument();
    expect(screen.getByText('Already have an account?')).toBeInTheDocument();
    expect(screen.getByText('Sign In')).toBeInTheDocument();
  });

  test('validates email format', () => {
    render(
      <BrowserRouter>
        <Auth onAuthSuccess={mockOnAuthSuccess} />
      </BrowserRouter>
    );
    
    // Enter invalid email and submit form
    fireEvent.change(screen.getByLabelText('Email Address'), { target: { value: 'invalid-email' } });
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } });
    fireEvent.click(screen.getByText('Sign In'));
    
    // Check if validation error is displayed
    expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
  });
});
