

const express = require('express'); // Imports the Express.js framework for creating the web server.
const cors = require('cors'); // Imports the CORS middleware to handle Cross-Origin Resource Sharing.
const nodemailer = require('nodemailer'); // Imports Nodemailer for sending emails.
require('dotenv').config(); // Loads environment variables from a .env file into process.env.

const app = express(); // Creates an instance of an Express application.

// Middleware
app.use(cors()); // Enables CORS for all routes, allowing requests from different origins.
app.use(express.json()); // Enables parsing of JSON request bodies, making it accessible in req.body.

// Email transporter setup
const transporter = nodemailer.createTransport({ // Creates a Nodemailer transporter object.
  service: 'gmail', // Specifies that Gmail will be used as the email service.
  auth: { // Authentication details for the Gmail account.
    user: process.env.EMAIL_USER, // Gets the email address from the EMAIL_USER environment variable.
    pass: process.env.EMAIL_PASS // Gets the email password from the EMAIL_PASS environment variable.
  }
});

// Route to handle order submission
app.post('/send-order', async (req, res) => { // Defines a POST route handler for /send-order.
  const { orderNumber, customerName, printType, quantity, price, fileName, address } = req.body; // Extracts order details from the request body.

  const mailOptions = { // Defines the email options.
    from: process.env.EMAIL_USER, // Sets the sender's email address from the environment variable.
    to: '<EMAIL>', // Sets the recipient's email address (the print shop's email).
    subject: `New Order: ${orderNumber}`, // Sets the email subject, including the order number.
    text: ` // Sets the email body as plain text.
      New Order: ${orderNumber}
      Customer: ${customerName}
      Print Type: ${printType}
      Quantity: ${quantity}
      Price: Php ${price}
      File: ${fileName || 'No file uploaded'} // If fileName is not provided, it defaults to 'No file uploaded'.
      Delivery Address: ${address}
    `
  };

  try { // Starts a try block to handle potential errors.
    await transporter.sendMail(mailOptions); // Sends the email using the transporter.
    console.log('Order email sent successfully'); // Logs a success message to the console.
    res.status(200).json({ message: 'Order placed successfully' }); // Sends a 200 OK response with a success message.
  } catch (error) { // Catches any errors that occur in the try block.
    console.error('Error sending order email:', error); // Logs the error to the console.
    res.status(500).json({ message: 'Failed to place order' }); // Sends a 500 Internal Server Error response with a failure message.
  }
});

const PORT = process.env.PORT || 3001; // Sets the port to listen on, using the PORT environment variable or 3001 as default.
app.listen(PORT, () => console.log(`Server running on port ${PORT}`)); // Starts the server and logs a message to the console.
