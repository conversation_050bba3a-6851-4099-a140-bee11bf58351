From 0d48facd2fce8b9546f553707f33d89dc2fd336b Mon Sep 17 00:00:00 2001
From: git-teng-test <<EMAIL>>
Date: Tue, 22 Apr 2025 20:21:36 +0800
Subject: [PATCH] Add CustomNumberInput component with visible
 increment/decrement buttons for file copies

---
 pixelprints-app/src/App.css                   |  6 +-
 pixelprints-app/src/App.js                    | 10 +-
 .../src/components/CustomNumberInput.css      | 93 +++++++++++++++++++
 .../src/components/CustomNumberInput.js       | 64 +++++++++++++
 4 files changed, 166 insertions(+), 7 deletions(-)
 create mode 100644 pixelprints-app/src/components/CustomNumberInput.css
 create mode 100644 pixelprints-app/src/components/CustomNumberInput.js

diff --git a/pixelprints-app/src/App.css b/pixelprints-app/src/App.css
index e086802..2ead798 100644
--- a/pixelprints-app/src/App.css
+++ b/pixelprints-app/src/App.css
@@ -504,6 +504,7 @@ input[type="file"]:focus {
   border-radius: 0;
   margin-bottom: 0.5rem;
   border-left: 3px solid #1976D2;
+  flex-wrap: wrap;
 }
 
 .file-item span {
@@ -675,9 +676,8 @@ input[type="file"]:focus {
     margin-bottom: 8px;
   }
 
-  .file-item input[type="number"] {
-    width: 60px;
-    margin-right: 10px;
+  .file-item .custom-number-input {
+    margin: 0 5px 0 0;
   }
 
   /* Form improvements */
diff --git a/pixelprints-app/src/App.js b/pixelprints-app/src/App.js
index 0cdc348..9a6dfa7 100644
--- a/pixelprints-app/src/App.js
+++ b/pixelprints-app/src/App.js
@@ -9,6 +9,7 @@ import { Routes, Route, useNavigate, Navigate } from "react-router-dom"
 import useDocumentTitle from "./hooks/useDocumentTitle"
 import { getApiUrl } from "./utils/api"
 import "./App.css"
+import CustomNumberInput from "./components/CustomNumberInput"
 import Auth from "./components/Auth/Auth"
 import ForgotPassword from "./components/Auth/ForgotPassword"
 import ResetPassword from "./components/Auth/ResetPassword"
@@ -797,11 +798,12 @@ function App() {
                       <span>
                         {file.name} ({file.pages} pages)
                       </span>
-                      <input
-                        type="number"
-                        min="1"
+                      <CustomNumberInput
                         value={file.copies}
-                        onChange={(e) => updateFileCopies(file.id, e.target.value)}
+                        onChange={(value) => updateFileCopies(file.id, value)}
+                        min={1}
+                        max={100}
+                        label="Copies"
                       />
                       <button type="button" onClick={() => removeFile(file.id)}>
                         REMOVE
diff --git a/pixelprints-app/src/components/CustomNumberInput.css b/pixelprints-app/src/components/CustomNumberInput.css
new file mode 100644
index 0000000..9a22737
--- /dev/null
+++ b/pixelprints-app/src/components/CustomNumberInput.css
@@ -0,0 +1,93 @@
+.custom-number-input {
+  display: flex;
+  flex-direction: column;
+  margin: 0 15px;
+}
+
+.number-input-label {
+  font-size: 0.85rem;
+  margin-bottom: 4px;
+  color: #555;
+}
+
+.number-input-container {
+  display: flex;
+  align-items: center;
+  height: 36px;
+  border: 1px solid #ddd;
+  border-radius: 0;
+  overflow: hidden;
+}
+
+.number-btn {
+  width: 36px;
+  height: 36px;
+  display: flex;
+  align-items: center;
+  justify-content: center;
+  background-color: #f0f0f0;
+  border: none;
+  font-size: 1.2rem;
+  font-weight: bold;
+  cursor: pointer;
+  user-select: none;
+  transition: background-color 0.2s;
+  padding: 0;
+}
+
+.number-btn:hover {
+  background-color: #e0e0e0;
+}
+
+.number-btn:active {
+  background-color: #d0d0d0;
+}
+
+.number-btn:disabled {
+  background-color: #f5f5f5;
+  color: #ccc;
+  cursor: not-allowed;
+}
+
+.number-input {
+  width: 40px;
+  height: 36px;
+  border: none;
+  text-align: center;
+  font-size: 1rem;
+  padding: 0;
+  -moz-appearance: textfield; /* Firefox */
+}
+
+.number-input::-webkit-outer-spin-button,
+.number-input::-webkit-inner-spin-button {
+  -webkit-appearance: none;
+  margin: 0;
+}
+
+.number-input:focus {
+  outline: none;
+}
+
+/* Mobile styles */
+@media (max-width: 480px) {
+  .custom-number-input {
+    margin: 0 8px;
+  }
+  
+  .number-input-container {
+    height: 32px;
+  }
+  
+  .number-btn {
+    width: 32px;
+    height: 32px;
+    font-size: 1rem;
+  }
+  
+  .number-input {
+    width: 36px;
+    height: 32px;
+    font-size: 0.9rem;
+  }
+}
diff --git a/pixelprints-app/src/components/CustomNumberInput.js b/pixelprints-app/src/components/CustomNumberInput.js
new file mode 100644
index 0000000..4f2e0c6
--- /dev/null
+++ b/pixelprints-app/src/components/CustomNumberInput.js
@@ -0,0 +1,64 @@
+import React from 'react';
+import './CustomNumberInput.css';
+
+const CustomNumberInput = ({ value, onChange, min = 1, max = 100, label = '' }) => {
+  const handleIncrement = () => {
+    const newValue = Math.min(max, parseInt(value, 10) + 1);
+    onChange(newValue);
+  };
+
+  const handleDecrement = () => {
+    const newValue = Math.max(min, parseInt(value, 10) - 1);
+    onChange(newValue);
+  };
+
+  const handleInputChange = (e) => {
+    const inputValue = e.target.value;
+    if (inputValue === '') {
+      onChange(min);
+    } else {
+      const numValue = parseInt(inputValue, 10);
+      if (!isNaN(numValue)) {
+        const boundedValue = Math.max(min, Math.min(max, numValue));
+        onChange(boundedValue);
+      }
+    }
+  };
+
+  return (
+    <div className="custom-number-input">
+      {label && <label className="number-input-label">{label}</label>}
+      <div className="number-input-container">
+        <button 
+          type="button" 
+          className="number-btn decrement-btn" 
+          onClick={handleDecrement}
+          aria-label="Decrease value"
+          disabled={value <= min}
+        >
+          -
+        </button>
+        <input
+          type="text"
+          inputMode="numeric"
+          pattern="[0-9]*"
+          value={value}
+          onChange={handleInputChange}
+          className="number-input"
+          aria-label={label || "Number input"}
+        />
+        <button 
+          type="button" 
+          className="number-btn increment-btn" 
+          onClick={handleIncrement}
+          aria-label="Increase value"
+          disabled={value >= max}
+        >
+          +
+        </button>
+      </div>
+    </div>
+  );
+};
+
+export default CustomNumberInput;
-- 
2.47.0.windows.1

