/**
 * Custom logger utility to control console output
 * This helps prevent sensitive information from being logged to the browser console
 */

// Environment check
const isDevelopment = process.env.NODE_ENV === 'development';

// Configuration for what types of logs are allowed in production
const PRODUCTION_CONFIG = {
  // Allow error logs in production for debugging critical issues
  allowErrors: true,
  // Don't allow informational logs in production
  allowInfo: false,
  // Don't allow debug logs in production
  allowDebug: false,
  // Don't allow warning logs in production
  allowWarnings: false,
  // Mask sensitive data in logs (passwords, tokens, etc.)
  maskSensitiveData: true
};

/**
 * Checks if a message contains sensitive information
 * @param {any} message - The message to check
 * @returns {boolean} - True if the message contains sensitive information
 */
const containsSensitiveInfo = (message) => {
  if (typeof message !== 'string') return false;
  
  // List of keywords that might indicate sensitive information
  const sensitiveKeywords = [
    'password', 'token', 'secret', 'key', 'auth', 'credential', 'admin',
    'credit', 'card', 'cvv', 'expiry', 'ssn', 'social', 'security',
    'license', 'address', 'phone', 'email', 'dob', 'birth'
  ];
  
  const lowerMessage = message.toLowerCase();
  return sensitiveKeywords.some(keyword => lowerMessage.includes(keyword));
};

/**
 * Masks sensitive information in objects
 * @param {any} data - The data to mask
 * @returns {any} - The masked data
 */
const maskSensitiveData = (data) => {
  if (!data) return data;
  
  // For strings, check if they contain sensitive info
  if (typeof data === 'string') {
    return containsSensitiveInfo(data) ? '[REDACTED]' : data;
  }
  
  // For arrays, mask each item
  if (Array.isArray(data)) {
    return data.map(item => maskSensitiveData(item));
  }
  
  // For objects, mask each property
  if (typeof data === 'object' && data !== null) {
    const maskedData = {};
    
    // List of property names that typically contain sensitive information
    const sensitiveProps = [
      'password', 'token', 'secret', 'key', 'auth', 'credential',
      'creditCard', 'cvv', 'expiry', 'ssn', 'socialSecurity',
      'licenseNumber', 'address', 'phoneNumber', 'email', 'dob', 'dateOfBirth'
    ];
    
    for (const prop in data) {
      if (Object.prototype.hasOwnProperty.call(data, prop)) {
        // If the property name indicates sensitive data, redact it
        if (sensitiveProps.some(sensitiveProp => prop.toLowerCase().includes(sensitiveProp.toLowerCase()))) {
          maskedData[prop] = '[REDACTED]';
        } else {
          // Otherwise, recursively mask the property value
          maskedData[prop] = maskSensitiveData(data[prop]);
        }
      }
    }
    
    return maskedData;
  }
  
  // For other types, return as is
  return data;
};

/**
 * Processes arguments before logging
 * @param {Array} args - The arguments to process
 * @returns {Array} - The processed arguments
 */
const processArgs = (args) => {
  if (!PRODUCTION_CONFIG.maskSensitiveData || isDevelopment) {
    return args;
  }
  
  return args.map(arg => {
    if (typeof arg === 'string') {
      return containsSensitiveInfo(arg) ? '[REDACTED]' : arg;
    }
    if (typeof arg === 'object' && arg !== null) {
      return maskSensitiveData(arg);
    }
    return arg;
  });
};

/**
 * Custom logger object that wraps console methods
 */
const logger = {
  /**
   * Log an error message
   * @param {...any} args - The arguments to log
   */
  error: (...args) => {
    // Always allow errors in development, check config for production
    if (isDevelopment || PRODUCTION_CONFIG.allowErrors) {
      console.error(...processArgs(args));
    }
  },
  
  /**
   * Log a warning message
   * @param {...any} args - The arguments to log
   */
  warn: (...args) => {
    // Allow warnings based on environment and config
    if (isDevelopment || PRODUCTION_CONFIG.allowWarnings) {
      console.warn(...processArgs(args));
    }
  },
  
  /**
   * Log an info message
   * @param {...any} args - The arguments to log
   */
  info: (...args) => {
    // Allow info logs based on environment and config
    if (isDevelopment || PRODUCTION_CONFIG.allowInfo) {
      console.log(...processArgs(args));
    }
  },
  
  /**
   * Log a debug message
   * @param {...any} args - The arguments to log
   */
  debug: (...args) => {
    // Allow debug logs based on environment and config
    if (isDevelopment || PRODUCTION_CONFIG.allowDebug) {
      console.log('[DEBUG]', ...processArgs(args));
    }
  }
};

export default logger;
