# Domain Configuration Guide for Pixel Prints

This guide explains how to configure Pixel Prints to work with multiple domains, specifically the new domain `pixelprints.it.com` alongside the existing `pixel-prints.netlify.app`.

## Backend Configuration (Render)

### 1. Environment Variables

Set the `FRONTEND_URL` environment variable in your Render dashboard to include both domains:

```
FRONTEND_URL=https://pixelprints.it.com,https://pixel-prints.netlify.app
```

This allows your backend to accept requests from both domains.

### 2. CORS Configuration

The backend has been updated to handle multiple domains in the CORS configuration. The server.js file now:

- Accepts an array of allowed origins
- Parses comma-separated domains from the FRONTEND_URL environment variable
- Removes duplicates
- Logs blocked origins for debugging

No additional configuration is needed as long as the environment variable is set correctly.

## Frontend Configuration (Netlify)

### 1. Domain Settings

In your Netlify dashboard:
- Go to "Domain settings"
- Ensure both domains are added
- Set `pixelprints.it.com` as the primary domain if desired

### 2. Environment Variables

The frontend uses the `REACT_APP_API_URL` environment variable to connect to the backend. This is set in the `netlify.toml` file:

```
REACT_APP_API_URL = "https://pixel-prints-1.onrender.com"
```

No changes are needed here as both domains will use the same backend URL.

### 3. Content Security Policy

The Content Security Policy in `netlify.toml` has been updated to include both domains:

```
Content-Security-Policy = "default-src 'self'; connect-src 'self' ... https://pixel-prints-1.onrender.com https://pixelprints.it.com; ..."
```

## DNS Configuration (Namecheap)

For the new domain to work with Netlify:

1. In Namecheap:
   - Set up a CNAME record for `www` pointing to `pixel-prints.netlify.app` (without https:// or trailing slash)
   - Set up an A record for the apex domain (@) pointing to Netlify's load balancer IP (*********)

2. In Netlify:
   - Verify domain ownership
   - Enable HTTPS

## Testing

After configuring both domains:

1. Test the application at both URLs:
   - https://pixelprints.it.com
   - https://pixel-prints.netlify.app

2. Verify that all features work correctly on both domains:
   - User authentication
   - Order placement
   - Admin dashboard
   - Comments and status updates

## Troubleshooting

If you encounter CORS issues:

1. Check the server logs for blocked origins
2. Verify that the FRONTEND_URL environment variable is set correctly
3. Clear your browser cache and try again
4. Check the browser console for specific error messages

## Long-term Considerations

1. **Domain Redirects**: Consider setting up redirects from the old domain to the new domain
2. **SEO**: Update any SEO settings to reflect the new primary domain
3. **Analytics**: Update analytics to track both domains
4. **Documentation**: Update documentation to reference the new domain
