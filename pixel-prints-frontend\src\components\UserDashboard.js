import React, { useState, useEffect } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import { formatDistanceToNow } from 'date-fns';

const UserDashboard = () => {
  const { user, isAuthenticated } = useAuth0();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user?.email) return;
      
      try {
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/user-dashboard/${user.email}`);
        setDashboardData(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard information');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  if (!isAuthenticated) {
    return <Typography>Please log in to view your dashboard</Typography>;
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Typography color="error">{error}</Typography>;
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        {/* Welcome Card */}
        <Grid item xs={12}>
          <Paper elevation={3} sx={{ p: 3, backgroundColor: '#f5f5f5' }}>
            <Typography variant="h4" gutterBottom>
              Welcome back, {dashboardData?.customerName}!
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Here's your printing dashboard
            </Typography>
          </Paper>
        </Grid>

        {/* Last Order Card */}
        <Grid item xs={12}>
          <Card elevation={3}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Last Order Status
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              {dashboardData?.lastOrder ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>Order Number:</strong> {dashboardData.lastOrder.orderNumber}
                    </Typography>
                    <Typography variant="body1">
                      <strong>Print Type:</strong> {dashboardData.lastOrder.printType}
                    </Typography>
                    <Typography variant="body1">
                      <strong>Total Pages:</strong> {dashboardData.lastOrder.totalPages}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>Price:</strong> ₱{dashboardData.lastOrder.price}
                    </Typography>
                    <Typography variant="body1">
                      <strong>Status:</strong>{' '}
                      <Box
                        component="span"
                        sx={{
                          color: 
                            dashboardData.lastOrder.status === 'completed' ? 'success.main' :
                            dashboardData.lastOrder.status === 'pending' ? 'warning.main' :
                            'error.main'
                        }}
                      >
                        {dashboardData.lastOrder.status.toUpperCase()}
                      </Box>
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Ordered {formatDistanceToNow(new Date(dashboardData.lastOrder.createdAt))} ago
                    </Typography>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body1" color="textSecondary">
                  No orders placed yet. Start printing today!
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default UserDashboard;