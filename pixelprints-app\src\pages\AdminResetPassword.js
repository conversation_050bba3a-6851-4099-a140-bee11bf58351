import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getApiUrl } from '../utils/api';
import './AdminLoginPage.css';

const AdminResetPassword = ({ onBackToMain }) => {
  const navigate = useNavigate();
  const { token } = useParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  useEffect(() => {
    // Validate token exists
    if (!token) {
      setMessage('Invalid password reset link.');
    }
  }, [token]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');
    setIsLoading(true);

    // Validate passwords match
    if (password !== confirmPassword) {
      setMessage('Passwords do not match.');
      setIsLoading(false);
      return;
    }

    // Validate password length
    if (password.length < 6) {
      setMessage('Password must be at least 6 characters long.');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(getApiUrl('/api/admin/reset-password'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSuccess(true);
        setMessage(data.message || 'Your admin password has been reset successfully.');
      } else {
        setMessage(data.message || 'An error occurred. Please try again.');
      }
    } catch (error) {
      console.error('Admin reset password error:', error);
      setMessage('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-login-page">
      <div className="admin-login-container">
        <div className="admin-login-header">
          <h1>Reset Admin Password</h1>
          <button className="back-button" onClick={onBackToMain}>
            Back to Main
          </button>
        </div>

        {isSuccess ? (
          <>
            <div className="success-message">
              <p>{message}</p>
            </div>
            <button
              type="button"
              className="admin-login-button"
              onClick={() => navigate('/admin-login')}
            >
              Go to Admin Login
            </button>
          </>
        ) : (
          <>
            <p>Enter your new admin password below.</p>

            {message && <div className={isSuccess ? 'success-message' : 'error-message'}>
              <p>{message}</p>
            </div>}

            <form className="admin-login-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="password">New Password</label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  minLength="6"
                  placeholder="Enter new password"
                />
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirm Password</label>
                <input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength="6"
                  placeholder="Confirm new password"
                />
              </div>

              <button
                type="submit"
                className="admin-login-button"
                disabled={isLoading}
              >
                {isLoading ? 'Resetting...' : 'Reset Password'}
              </button>
            </form>

            <div className="admin-login-footer">
              <button
                type="button"
                className="switch-button"
                onClick={() => navigate('/admin-login')}
              >
                Back to Admin Login
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminResetPassword;
