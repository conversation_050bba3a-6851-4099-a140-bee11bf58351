# Wait for the database to be ready
Write-Host "Waiting for database to be ready..."
Start-Sleep -Seconds 10

# Initialize the database
Write-Host "Initializing database..."
docker-compose exec backend node db/init.js
docker-compose exec backend node db/setup_settings.js
docker-compose exec backend node db/create_admin.js
docker-compose exec backend node db/add_site_notification_settings.js

Write-Host "Database initialization complete!"
