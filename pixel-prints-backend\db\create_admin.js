const bcrypt = require('bcryptjs');
const db = require('../db');
require('dotenv').config();

async function createAdminUser() {
  try {
    // Check if users table exists
    const tableCheck = await db.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      );
    `);

    // If users table doesn't exist, create it
    if (!tableCheck.rows[0].exists) {
      console.log('Creating users table...');
      await db.query(`
        CREATE TABLE users (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          email VARCHAR(100) NOT NULL UNIQUE,
          password VARCHAR(255) NOT NULL,
          role VARCHAR(20) DEFAULT 'user',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('Users table created successfully.');
    }

    // Get admin email from environment variables
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    // Check if admin user already exists
    const adminCheck = await db.query(`
      SELECT * FROM users WHERE email = $1;
    `, [adminEmail]);

    if (adminCheck.rows.length > 0) {
      console.log('Admin user already exists.');
      process.exit(0);
    }

    // Create admin user
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(adminPassword, salt);

    await db.query(`
      INSERT INTO users (name, email, password, role)
      VALUES ('Admin', $1, $2, 'admin');
    `, [adminEmail, hashedPassword]);

    console.log('Admin user created successfully.');
    console.log(`Email: ${adminEmail}`);
    console.log(`Password: ${adminPassword}`);

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdminUser();
