.assisted-locations {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.locations-header {
  margin-bottom: 20px;
}

.locations-header h2 {
  margin: 0 0 10px;
  color: #333;
}

.locations-header p {
  color: #666;
  margin: 0;
}

.locations-container {
  margin-bottom: 20px;
}

.region-group {
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.region-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f5f5f5;
  font-weight: bold;
  border-radius: 4px 4px 0 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.region-group-header > div {
  flex: 1;
  cursor: pointer;
}

.expand-icon {
  cursor: pointer;
  padding: 5px;
}

.region-group-header:hover {
  background-color: #eeeeee;
}

.regions {
  padding: 10px 15px;
}

.region {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.region:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.region-header {
  margin-bottom: 10px;
  font-weight: 500;
}

.provinces {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-left: 20px;
}

.province {
  padding: 5px 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-container input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
  width: 18px;
  height: 18px;
  accent-color: #1976D2;
}

.checkbox-container label {
  cursor: pointer;
  user-select: none; /* Prevent text selection when clicking */
}

.expand-icon {
  font-size: 12px;
  color: #666;
}

.assisted-summary {
  margin-top: 30px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 4px solid #1976D2;
}

.assisted-summary h3 {
  margin: 0 0 15px;
  color: #333;
}

.assisted-provinces-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.assisted-province {
  display: inline-block;
  padding: 5px 10px;
  background-color: #e3f2fd;
  border-radius: 4px;
  color: #1976D2;
  font-size: 0.9rem;
}

.actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.save-button {
  padding: 10px 20px;
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: background-color 0.3s;
}

.save-button:hover {
  background-color: #1565C0;
}

.save-status {
  padding: 10px 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.save-status.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
}

.save-status.error {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #c62828;
}

/* Custom checkbox styling */
.checkbox-container input[type="checkbox"]:indeterminate {
  background-color: #bbdefb;
  border-color: #1976D2;
}

/* Responsive styles */
@media (max-width: 768px) {
  .assisted-locations {
    padding: 15px;
  }

  .provinces {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .region-group-header {
    padding: 10px;
  }

  .regions {
    padding: 10px;
  }

  .assisted-provinces-list {
    flex-wrap: wrap;
  }

  .assisted-province {
    margin: 3px;
    font-size: 0.85rem;
  }

  .save-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .assisted-locations {
    padding: 10px;
  }

  .locations-header h2 {
    font-size: 1.2rem;
  }

  .locations-header p {
    font-size: 0.9rem;
  }

  .region-group-header {
    font-size: 0.9rem;
  }

  .provinces {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }

  .province {
    font-size: 0.85rem;
  }

  .checkbox-container input[type="checkbox"] {
    width: 16px;
    height: 16px;
  }

  .save-button {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  /* Improve assisted provinces summary on mobile */
  .assisted-summary {
    padding: 10px;
    margin-top: 20px;
    width: 100%;
    box-sizing: border-box;
  }

  .assisted-summary h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    width: 100%;
  }

  .assisted-provinces-list {
    gap: 5px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    width: 100%;
  }

  .assisted-province {
    padding: 5px 8px;
    font-size: 0.8rem;
    margin: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
  }
}
