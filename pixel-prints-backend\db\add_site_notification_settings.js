const db = require('../db');
require('dotenv').config();

async function addSiteNotificationSettings() {
  try {
    // Check if settings already exist
    const checkResult = await db.query(
      'SELECT * FROM settings WHERE key = $1 OR key = $2',
      ['site_notification_message', 'site_notification_enabled']
    );

    if (checkResult.rows.length === 0) {
      // Add site notification settings
      await db.query(`
        INSERT INTO settings (key, value, description)
        VALUES
          ('site_notification_message', 'The website is currently undergoing maintenance. Some features may be unavailable.', 'Message to display as site notification'),
          ('site_notification_enabled', 'false', 'Whether the site notification is enabled');
      `);
      console.log('Site notification settings added successfully');
    } else {
      console.log('Site notification settings already exist');
    }

    process.exit(0);
  } catch (error) {
    console.error('Error adding site notification settings:', error);
    process.exit(1);
  }
}

addSiteNotificationSettings();
