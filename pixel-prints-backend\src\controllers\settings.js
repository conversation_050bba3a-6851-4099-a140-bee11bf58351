const db = require('../../db');

// Get all settings
const getAllSettings = async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM settings');
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({ message: 'Failed to fetch settings' });
  }
};

// Get a setting by key
const getSettingByKey = async (req, res) => {
  const { key } = req.params;

  try {
    const result = await db.query('SELECT * FROM settings WHERE key = $1', [key]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Setting not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error(`Error fetching setting with key ${key}:`, error);
    res.status(500).json({ message: 'Failed to fetch setting' });
  }
};

// Update a setting
const updateSetting = async (req, res) => {
  const { key } = req.params;
  const { value, description } = req.body;

  try {
    // First check if the setting exists
    const checkResult = await db.query('SELECT * FROM settings WHERE key = $1', [key]);

    if (checkResult.rows.length === 0) {
      // Setting doesn't exist, create it
      const insertResult = await db.query(
        'INSERT INTO settings (key, value, description) VALUES ($1, $2, $3) RETURNING *',
        [key, value, description]
      );

      return res.json(insertResult.rows[0]);
    }

    // Setting exists, update it
    const result = await db.query(
      'UPDATE settings SET value = $1, description = $2, updated_at = CURRENT_TIMESTAMP WHERE key = $3 RETURNING *',
      [value, description, key]
    );

    res.json(result.rows[0]);
  } catch (error) {
    console.error(`Error updating setting with key ${key}:`, error);
    res.status(500).json({ message: 'Failed to update setting' });
  }
};

// Check if daily order limit is reached
const checkDailyOrderLimit = async (req, res) => {
  try {
    // Get the daily order limit setting
    const limitResult = await db.query('SELECT value FROM settings WHERE key = $1', ['daily_order_limit']);
    const enabledResult = await db.query('SELECT value FROM settings WHERE key = $1', ['daily_order_limit_enabled']);

    if (limitResult.rows.length === 0 || enabledResult.rows.length === 0) {
      return res.status(404).json({ message: 'Daily order limit setting not found' });
    }

    const dailyOrderLimit = parseInt(limitResult.rows[0].value, 10);
    const isEnabled = enabledResult.rows[0].value === 'true';

    // If limit is not enabled, return false
    if (!isEnabled) {
      return res.json({
        limitReached: false,
        ordersToday: 0,
        limit: dailyOrderLimit,
        enabled: isEnabled
      });
    }

    // Get the count of orders for today
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const orderCountResult = await db.query(
      'SELECT COUNT(*) FROM orders WHERE created_at >= $1',
      [today]
    );

    const ordersToday = parseInt(orderCountResult.rows[0].count, 10);
    const limitReached = ordersToday >= dailyOrderLimit;

    res.json({
      limitReached,
      ordersToday,
      limit: dailyOrderLimit,
      enabled: isEnabled
    });
  } catch (error) {
    console.error('Error checking daily order limit:', error);
    res.status(500).json({ message: 'Failed to check daily order limit' });
  }
};

module.exports = {
  getAllSettings,
  getSettingByKey,
  updateSetting,
  checkDailyOrderLimit
};
