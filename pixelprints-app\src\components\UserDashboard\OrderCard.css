/* Order Card Styles */
.order-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.order-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Order Card Header */
.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.2rem;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.3rem;
}

.order-date {
  font-size: 0.9rem;
  color: #666;
}

/* Status Badge */
.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.status-processing {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-cancelled {
  background-color: #ffebee;
  color: #c62828;
}

.status-default {
  background-color: #f5f5f5;
  color: #616161;
}

/* Order Details */
.order-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.2rem;
}

.detail-group {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 0.3rem;
}

.detail-value {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

/* Order Footer */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.price-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-label {
  font-size: 0.9rem;
  color: #666;
}

.price-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #1976D2;
}

.view-details-hint {
  font-size: 0.8rem;
  color: #666;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.order-card:hover .view-details-hint {
  opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .order-details {
    grid-template-columns: 1fr 1fr;
  }
  
  .order-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
  }
  
  .view-details-hint {
    opacity: 1;
  }
}
