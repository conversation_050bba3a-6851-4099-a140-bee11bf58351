const axios = require('axios');

const testOrder = {
  orderNumber: 'TEST123',
  customerName: 'Test User',
  printType: 'black',
  quantity: 2,
  price: '5.00',
  fileName: 'test.pdf',
  address: '123 Test St, Test City, 12345'
};

async function testBackend() {
  try {
    const response = await axios.post('http://localhost:3001/send-order', testOrder);
    console.log('Test successful:', response.data);
  } catch (error) {
    console.error('Test failed:', error.response ? error.response.data : error.message);
  }
}

testBackend();