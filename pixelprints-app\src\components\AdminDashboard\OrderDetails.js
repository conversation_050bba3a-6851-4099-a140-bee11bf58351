import React from 'react';
import '../UserDashboard/CommentSection.css';
import CommentSection from '../UserDashboard/CommentSection';

const OrderDetails = ({ order, onClose, updateOrderStatus }) => {
  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Parse files JSON if it exists
  let files = [];
  try {
    if (order.files) {
      if (typeof order.files === 'string') {
        files = JSON.parse(order.files);
      } else if (Array.isArray(order.files)) {
        files = order.files;
      }
    }
  } catch (error) {
    console.error('Error parsing files JSON:', error);
  }

  return (
    <div className="order-details-modal">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Order #{order.order_number}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <div className="order-info">
            <div className="info-section">
              <h3>Customer Information</h3>
              <p><strong>Name:</strong> {order.customer_name}</p>
              <p><strong>Email:</strong> {order.customer_email}</p>
              <p><strong>Address:</strong> {order.address}</p>
              {order.province && <p><strong>Province:</strong> {order.province}</p>}
            </div>

            <div className="info-section">
              <h3>Order Information</h3>
              <p><strong>Order Date:</strong> {formatDate(order.created_at)}</p>
              <p><strong>Print Type:</strong> {order.print_type}</p>
              <p><strong>Total Pages:</strong> {order.total_pages}</p>
              <p><strong>Price:</strong> ₱{parseFloat(order.price || 0).toFixed(2)}</p>
              {parseFloat(order.delivery_charge || 0) > 0 && (
                <p><strong>Delivery Charge:</strong> ₱{parseFloat(order.delivery_charge).toFixed(2)}</p>
              )}
              <p><strong>Status:</strong> {order.status}</p>
            </div>
          </div>

          {files.length > 0 && (
            <div className="files-section">
              <h3>Files</h3>
              <ul className="files-list">
                {files.map((file, index) => (
                  <li key={index}>
                    <p><strong>File:</strong> {file.name}</p>
                    <p><strong>Pages:</strong> {file.pages}</p>
                    <p><strong>Copies:</strong> {file.copies}</p>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="status-actions">
            <h3>Update Status</h3>
            <div className="status-buttons">
              <button
                className={`status-button ${order.status === 'pending' ? 'active' : ''}`}
                onClick={() => updateOrderStatus(order.id, 'pending')}
              >
                Pending
              </button>
              <button
                className={`status-button ${order.status === 'processing' ? 'active' : ''}`}
                onClick={() => updateOrderStatus(order.id, 'processing')}
              >
                Processing
              </button>
              <button
                className={`status-button ${order.status === 'completed' ? 'active' : ''}`}
                onClick={() => updateOrderStatus(order.id, 'completed')}
              >
                Completed
              </button>
              <button
                className={`status-button ${order.status === 'delayed' ? 'active' : ''}`}
                onClick={() => updateOrderStatus(order.id, 'delayed')}
              >
                Delayed
              </button>
              <button
                className={`status-button ${order.status === 'cancelled' ? 'active' : ''}`}
                onClick={() => updateOrderStatus(order.id, 'cancelled')}
              >
                Cancelled
              </button>
            </div>
          </div>

          {/* Customer Comments */}
          <div className="comments-container" style={{ marginTop: '2rem', borderTop: '1px solid #eee', paddingTop: '1.5rem' }}>
            <CommentSection
              orderId={order.id}
              userName="Admin"
            />
          </div>
        </div>

        <div className="modal-footer">
          <button className="close-button" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
