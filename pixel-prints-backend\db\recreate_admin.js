const bcrypt = require('bcryptjs');
const db = require('../db');
require('dotenv').config();

async function recreateAdminUser() {
  try {
    // Get admin email from environment variables
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    // Delete existing admin user
    await db.query('DELETE FROM users WHERE email = $1', [adminEmail]);
    console.log(`Deleted existing admin user with email ${adminEmail} (if any)`);

    // Create admin user with credentials from environment variables

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(adminPassword, salt);

    await db.query(`
      INSERT INTO users (name, email, password, role)
      VALUES ('Admin', $1, $2, 'admin');
    `, [adminEmail, hashedPassword]);

    console.log('Admin user created successfully.');
    console.log(`Email: ${adminEmail}`);
    console.log(`Password: ${adminPassword}`);

    process.exit(0);
  } catch (error) {
    console.error('Error recreating admin user:', error);
    process.exit(1);
  }
}

recreateAdminUser();
