-- Create settings table for application settings
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO settings (key, value, description)
VALUES 
('daily_order_limit', '10', 'Maximum number of orders allowed per day'),
('daily_order_limit_enabled', 'true', 'Whether the daily order limit is enabled');
