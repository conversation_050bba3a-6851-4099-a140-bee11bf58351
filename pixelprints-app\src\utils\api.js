import logger from './logger';

// API base URL from environment variables
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

// Helper function to build API URLs
export const getApiUrl = (endpoint) => {
  // Make sure endpoint starts with a slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${API_BASE_URL}${formattedEndpoint}`;
};

// Helper function to make API requests with CORS mode
export const fetchApi = async (endpoint, options = {}) => {
  const url = getApiUrl(endpoint);

  // Add CORS mode
  const fetchOptions = {
    ...options,
    mode: 'cors',
    // Removed credentials: 'include' which can cause CORS issues
    headers: {
      ...options.headers,
      'Content-Type': 'application/json',
    }
  };

  try {
    const response = await fetch(url, fetchOptions);
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API request failed with status ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    // Use our custom logger instead of console.error
    logger.error(`Error fetching ${url}:`, error);
    throw error;
  }
};

export default {
  getApiUrl
};
