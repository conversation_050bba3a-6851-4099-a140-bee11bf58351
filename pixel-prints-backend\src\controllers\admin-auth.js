const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const db = require('../../db');
const nodemailer = require('nodemailer');

// Email transporter setup
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

// JWT secret key
const JWT_SECRET = process.env.JWT_SECRET || 'admin-secret-key';

// Admin login
const adminLogin = async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({ message: 'Email and password are required' });
  }

  try {
    // Check if the user exists and is an admin
    const result = await db.query(
      'SELECT * FROM users WHERE email = $1 AND role = $2',
      [email, 'admin']
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ message: 'Invalid credentials or not an admin' });
    }

    const admin = result.rows[0];

    // If there's no password in the database (for testing), allow any password
    if (!admin.password) {
      // Generate JWT token
      const token = jwt.sign(
        {
          id: admin.id,
          email: admin.email,
          name: admin.name,
          role: 'admin'
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      return res.json({
        user: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: 'admin'
        },
        token
      });
    }

    // Compare passwords
    const isPasswordValid = await bcrypt.compare(password, admin.password);

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: 'admin'
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Return admin info and token
    res.json({
      user: {
        id: admin.id,
        name: admin.name,
        email: admin.email,
        role: 'admin'
      },
      token
    });
  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ message: 'Server error during login' });
  }
};

// Create admin user (for initial setup)
const createAdmin = async (req, res) => {
  const { name, email, password } = req.body;

  if (!name || !email || !password) {
    return res.status(400).json({ message: 'Name, email, and password are required' });
  }

  try {
    // Check if admin already exists
    const existingAdmin = await db.query('SELECT * FROM users WHERE email = $1', [email]);

    if (existingAdmin.rows.length > 0) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create admin user
    const result = await db.query(
      'INSERT INTO users (name, email, password, role) VALUES ($1, $2, $3, $4) RETURNING id, name, email, role',
      [name, email, hashedPassword, 'admin']
    );

    const newAdmin = result.rows[0];

    res.status(201).json({
      message: 'Admin user created successfully',
      user: {
        id: newAdmin.id,
        name: newAdmin.name,
        email: newAdmin.email,
        role: newAdmin.role
      }
    });
  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({ message: 'Server error during admin creation' });
  }
};

// Admin forgot password - request password reset
const adminForgotPassword = async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ message: 'Email is required' });
  }

  try {
    // Check if admin user exists
    const result = await db.query('SELECT * FROM users WHERE email = $1 AND role = $2', [email, 'admin']);

    if (result.rows.length === 0) {
      // Don't reveal that the user doesn't exist for security reasons
      return res.status(200).json({ message: 'If your email is registered as an admin, you will receive a password reset link' });
    }

    const admin = result.rows[0];

    // Generate a random reset token
    const resetToken = crypto.randomBytes(32).toString('hex');

    // Set token expiration (1 hour from now)
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour

    // Save the reset token and expiration to the database
    await db.query(
      'UPDATE users SET reset_token = $1, reset_token_expires = $2 WHERE id = $3',
      [resetToken, resetTokenExpires, admin.id]
    );

    // Create reset URL
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/admin-reset-password/${resetToken}`;

    // Email content
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: admin.email,
      subject: 'Admin Password Reset Request',
      text: `
        You are receiving this email because you (or someone else) has requested to reset your admin password.

        Please click on the following link to reset your password:
        ${resetUrl}

        If you did not request this, please ignore this email and your password will remain unchanged.

        This link will expire in 1 hour.
      `
    };

    // Send email
    await transporter.sendMail(mailOptions);

    res.status(200).json({ message: 'If your email is registered as an admin, you will receive a password reset link' });
  } catch (error) {
    console.error('Admin forgot password error:', error);
    res.status(500).json({ message: 'Server error during password reset request' });
  }
};

// Admin reset password with token
const adminResetPassword = async (req, res) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return res.status(400).json({ message: 'Token and new password are required' });
  }

  try {
    // Find admin user with the given reset token and check if it's still valid
    const result = await db.query(
      'SELECT * FROM users WHERE reset_token = $1 AND reset_token_expires > $2 AND role = $3',
      [token, new Date(), 'admin']
    );

    if (result.rows.length === 0) {
      return res.status(400).json({ message: 'Invalid or expired password reset token' });
    }

    const admin = result.rows[0];

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update admin's password and clear the reset token fields
    await db.query(
      'UPDATE users SET password = $1, reset_token = NULL, reset_token_expires = NULL WHERE id = $2',
      [hashedPassword, admin.id]
    );

    res.status(200).json({ message: 'Password has been reset successfully' });
  } catch (error) {
    console.error('Admin reset password error:', error);
    res.status(500).json({ message: 'Server error during password reset' });
  }
};

module.exports = {
  adminLogin,
  createAdmin,
  adminForgotPassword,
  adminResetPassword
};
