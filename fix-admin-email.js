const bcrypt = require('bcryptjs');
const db = require('./db');

async function fixAdminEmail() {
  try {
    console.log('Checking for admin user...');
    
    // <NAME_EMAIL> exists
    const adminCheck = await db.query(`
      SELECT * FROM users WHERE email = '<EMAIL>';
    `);
    
    if (adminCheck.rows.length > 0) {
      console.log('Admin user <NAME_EMAIL> already exists.');
    } else {
      console.log('Admin user <NAME_EMAIL> does not exist. Creating it...');
      
      // Create admin user with password 'admin123'
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      await db.query(`
        INSERT INTO users (name, email, password, role)
        VALUES ('Admin', '<EMAIL>', $1, 'admin');
      `, [hashedPassword]);
      
      console.log('Admin user created successfully.');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
    }
    
    // Verify all admin users
    const allAdmins = await db.query(`
      SELECT id, name, email, role FROM users WHERE role = 'admin';
    `);
    
    console.log('All admin users:');
    allAdmins.rows.forEach(admin => {
      console.log(`- ${admin.email} (ID: ${admin.id})`);
    });
    
  } catch (error) {
    console.error('Error fixing admin email:', error);
  } finally {
    // Close the database connection
    if (db.end) {
      await db.end();
    }
    console.log('Database connection closed.');
  }
}

fixAdminEmail();
