import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getApiUrl, fetchApi } from '../utils/api';
import './AdminLoginPage.css';

const AdminLoginPage = ({ onLoginSuccess, onBackToMain }) => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      console.log('Attempting to login with:', { email, password });

      const data = await fetchApi('/api/admin/login', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });

      console.log('Response data:', data);

      // Store admin data
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('userName', data.user.name);
      localStorage.setItem('userEmail', data.user.email);
      localStorage.setItem('userRole', 'admin');
      localStorage.setItem('adminToken', data.token);

      // Call the success callback
      if (onLoginSuccess) {
        onLoginSuccess(data.user);
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-login-page">
      <div className="admin-login-container">
        <div className="admin-login-header">
          <h1>Admin Login</h1>
          <button className="back-button" onClick={onBackToMain}>
            Back to Main
          </button>
        </div>

        {error && <div className="error-message">
          <p>{error}</p>
        </div>}

        <form className="admin-login-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="admin-email">Email</label>
            <input
              id="admin-email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="admin-password">Password</label>
            <input
              id="admin-password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="••••••••"
              required
            />
          </div>

          <div className="forgot-password">
            <button
              type="button"
              className="forgot-password-link"
              onClick={() => navigate('/admin-forgot-password')}
            >
              Forgot Password?
            </button>
          </div>

          <button
            type="submit"
            className="admin-login-button"
            disabled={isLoading}
          >
            {isLoading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AdminLoginPage;
