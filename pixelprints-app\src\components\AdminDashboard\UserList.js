import React, { useState } from 'react';
import './UserList.css';
import UserForm from './UserForm';
import DeleteConfirmation from './DeleteConfirmation';
import { getApiUrl } from '../../utils/api';

const UserList = ({ users, onRefresh, loading }) => {
  // State for user management
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [deletingUser, setDeletingUser] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [actionMessage, setActionMessage] = useState(null);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Calculate user statistics with error handling
  const userStats = {
    admin: users.filter(user => user && user.role === 'admin').length,
    user: users.filter(user => user && user.role === 'user').length,
    customer: users.filter(user => user && user.role === 'customer').length,
    total: Array.isArray(users) ? users.length : 0
  };

  // Handle adding a new user
  const handleAddUser = async (userData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(getApiUrl('/api/users/admin-dashboard'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to add user');
      }

      setActionMessage({ type: 'success', text: 'User added successfully' });
      setShowAddForm(false);
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error adding user:', error);
      setActionMessage({ type: 'error', text: error.message || 'Failed to add user' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle updating a user
  const handleUpdateUser = async (userData) => {
    if (!editingUser || !editingUser.id) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(getApiUrl(`/api/users/admin-dashboard/${editingUser.id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to update user');
      }

      setActionMessage({ type: 'success', text: 'User updated successfully' });
      setEditingUser(null);
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error updating user:', error);
      setActionMessage({ type: 'error', text: error.message || 'Failed to update user' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a user
  const handleDeleteUser = async () => {
    if (!deletingUser || !deletingUser.id) return;

    setIsDeleting(true);
    try {
      const response = await fetch(getApiUrl(`/api/users/admin-dashboard/${deletingUser.id}`), {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to delete user');
      }

      setActionMessage({ type: 'success', text: 'User deleted successfully' });
      setDeletingUser(null);
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error deleting user:', error);
      setActionMessage({ type: 'error', text: error.message || 'Failed to delete user' });
    } finally {
      setIsDeleting(false);
    }
  };

  // Clear action message after 5 seconds
  if (actionMessage) {
    setTimeout(() => {
      setActionMessage(null);
    }, 5000);
  }

  return (
    <div className="user-list">
      {/* Action message */}
      {actionMessage && (
        <div className={`action-message ${actionMessage.type}`}>
          {actionMessage.text}
        </div>
      )}

      {/* User form for adding/editing */}
      {showAddForm && (
        <UserForm
          onSubmit={handleAddUser}
          onCancel={() => setShowAddForm(false)}
          isEditing={false}
          isSubmitting={isSubmitting}
        />
      )}

      {editingUser && (
        <UserForm
          user={editingUser}
          onSubmit={handleUpdateUser}
          onCancel={() => setEditingUser(null)}
          isEditing={true}
          isSubmitting={isSubmitting}
        />
      )}

      {/* Delete confirmation */}
      {deletingUser && (
        <DeleteConfirmation
          user={deletingUser}
          onConfirm={handleDeleteUser}
          onCancel={() => setDeletingUser(null)}
          isDeleting={isDeleting}
        />
      )}

      {/* Only show the main UI if not adding/editing/deleting */}
      {!showAddForm && !editingUser && !deletingUser && (
        <>
          <div className="user-list-header">
            <h3>Registered Users and Customers</h3>
            <div className="user-list-actions">
              <button
                className="add-user-button"
                onClick={() => setShowAddForm(true)}
              >
                Add New User
              </button>
              {onRefresh && (
                <button
                  className="refresh-button"
                  onClick={onRefresh}
                  disabled={loading}
                >
                  {loading ? 'Refreshing...' : 'Refresh Users'}
                </button>
              )}
            </div>
          </div>

      {/* User count summary */}
      <div className="user-count-summary">
        <div className="user-count-item total">
          <span className="user-count-label">Total</span>
          <span className="user-count-value">{userStats.total}</span>
        </div>
        <div className="user-count-item admin">
          <span className="user-count-label">Admins</span>
          <span className="user-count-value">{userStats.admin}</span>
        </div>
        <div className="user-count-item user">
          <span className="user-count-label">Users</span>
          <span className="user-count-value">{userStats.user}</span>
        </div>
        <div className="user-count-item customer">
          <span className="user-count-label">Customers</span>
          <span className="user-count-value">{userStats.customer}</span>
        </div>
      </div>

      {users.length === 0 ? (
        <p className="no-users">No users found.</p>
      ) : (
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Name</th>
              <th>Email</th>
              <th>Role</th>
              <th>Joined</th>
              <th>Source</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(users) && users.map(user => {
              // Skip invalid users
              if (!user || !user.email) return null;
              // Determine user source based on ID format
              let source = 'Unknown';
              if (user.id) {
                // Convert ID to string to safely use string methods
                const idStr = String(user.id);

                if (idStr.startsWith('c-')) source = 'Order';
                else if (idStr.startsWith('u-')) source = 'Registration';
                else if (idStr.startsWith('db-')) source = 'Database';
                else if (idStr === 'admin-1') source = 'System';
                else if (!isNaN(Number(idStr))) source = 'Database';
              }

              return (
                <tr key={user.id || user.email}>
                  <td>{user.id || '-'}</td>
                  <td>{user.name}</td>
                  <td>{user.email}</td>
                  <td>
                    <span className={`role-badge ${user.role || 'user'}`}>
                      {user.role || 'user'}
                    </span>
                  </td>
                  <td>{user.created_at ? formatDate(user.created_at) : 'N/A'}</td>
                  <td>{source}</td>
                  <td className="action-buttons">
                    {/* Only show edit/delete for database users */}
                    {(source === 'Database' || source === 'System') && (
                      <>
                        <button
                          className="edit-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingUser(user);
                          }}
                          title="Edit User"
                        >
                          ✏️ Edit
                        </button>
                        <button
                          className="delete-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeletingUser(user);
                          }}
                          title="Delete User"
                          disabled={user.role === 'admin' && userStats.admin <= 1}
                        >
                          🗑️ Delete
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}
      </>
      )}
    </div>
  );
};

export default UserList;
