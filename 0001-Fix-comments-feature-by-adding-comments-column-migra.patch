From 9f971967abf7037d8dd3e0bfd314891cee6a9fb2 Mon Sep 17 00:00:00 2001
From: git-teng-test <<EMAIL>>
Date: Tue, 22 Apr 2025 18:33:39 +0800
Subject: [PATCH 1/2] Fix comments feature by adding comments column migration
 and improving error handling

---
 pixel-prints-backend/run-migrations.js |  38 ++++++++
 pixel-prints-backend/server.js         | 115 ++++++++++++++++++++++++-
 2 files changed, 150 insertions(+), 3 deletions(-)
 create mode 100644 pixel-prints-backend/run-migrations.js

diff --git a/pixel-prints-backend/run-migrations.js b/pixel-prints-backend/run-migrations.js
new file mode 100644
index 0000000..cb39aec
--- /dev/null
+++ b/pixel-prints-backend/run-migrations.js
@@ -0,0 +1,38 @@
+const db = require('./db');
+
+async function runMigrations() {
+  try {
+    console.log('Running database migrations...');
+    
+    // Check if comments column exists in orders table
+    const checkResult = await db.query(`
+      SELECT column_name 
+      FROM information_schema.columns 
+      WHERE table_name = 'orders' AND column_name = 'comments'
+    `);
+
+    if (checkResult.rows.length === 0) {
+      console.log('Adding comments column to orders table...');
+      
+      // Add comments column as JSONB with default empty array
+      await db.query(`
+        ALTER TABLE orders 
+        ADD COLUMN comments JSONB DEFAULT '[]'::jsonb
+      `);
+      
+      console.log('Comments column added successfully');
+    } else {
+      console.log('Comments column already exists in orders table');
+    }
+    
+    console.log('Migrations completed successfully');
+  } catch (error) {
+    console.error('Error running migrations:', error);
+  } finally {
+    // Close the database connection
+    await db.end();
+  }
+}
+
+// Run the migrations
+runMigrations();
diff --git a/pixel-prints-backend/server.js b/pixel-prints-backend/server.js
index 0e44c37..e1a8b46 100644
--- a/pixel-prints-backend/server.js
+++ b/pixel-prints-backend/server.js
@@ -5,6 +5,7 @@ const nodemailer = require("nodemailer")
 const cors = require("cors")
 const fs = require("fs")
 const path = require("path")
+const bcrypt = require("bcryptjs")
 require("dotenv").config()
 const db = require("./db")
 
@@ -434,11 +435,84 @@ app.get("/", (req, res) => {
 // Serve uploaded files
 app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
 
+// Ensure admin user exists
+const ensureAdmin = async () => {
+  try {
+    console.log('Checking for admin user...');
+
+    // Check if admin user exists
+    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
+    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
+
+    console.log(`Looking for admin with email: ${adminEmail}`);
+
+    const result = await db.query('SELECT * FROM users WHERE email = $1 AND role = $2', [adminEmail, 'admin']);
+
+    if (result.rows.length === 0) {
+      console.log('No admin user found. Creating admin user...');
+
+      // Hash password
+      const salt = await bcrypt.genSalt(10);
+      const hashedPassword = await bcrypt.hash(adminPassword, salt);
+
+      // Create admin user
+      const insertResult = await db.query(
+        'INSERT INTO users (name, email, password, role) VALUES ($1, $2, $3, $4) RETURNING id, name, email, role',
+        ['Admin User', adminEmail, hashedPassword, 'admin']
+      );
+
+      console.log('Admin user created successfully:', insertResult.rows[0]);
+    } else {
+      console.log('Admin user already exists:', result.rows[0].email);
+    }
+  } catch (error) {
+    console.error('Error ensuring admin exists:', error);
+  }
+};
+
+// Ensure comments column exists in orders table
+const ensureCommentsColumn = async () => {
+  try {
+    console.log('Checking for comments column in orders table...');
+
+    // Check if comments column exists
+    const checkResult = await db.query(`
+      SELECT column_name
+      FROM information_schema.columns
+      WHERE table_name = 'orders' AND column_name = 'comments'
+    `);
+
+    if (checkResult.rows.length === 0) {
+      console.log('Adding comments column to orders table...');
+
+      // Add comments column as JSONB with default empty array
+      await db.query(`
+        ALTER TABLE orders
+        ADD COLUMN comments JSONB DEFAULT '[]'::jsonb
+      `);
+
+      console.log('Comments column added successfully');
+    } else {
+      console.log('Comments column already exists in orders table');
+    }
+  } catch (error) {
+    console.error('Error ensuring comments column exists:', error);
+  }
+};
+
 // Start server
 const PORT = process.env.PORT || 3001
-app.listen(PORT, () => {
+app.listen(PORT, async () => {
   console.log(`Server is running on port ${PORT}`)
   console.log(`Uploads directory: ${uploadsDir}`)
+
+  // Run database migrations
+  await ensureCommentsColumn();
+
+  // Ensure admin user exists
+  await ensureAdmin();
+
+  console.log('Server initialization completed');
 })
 
 // Add this near your other endpoints
@@ -554,7 +628,23 @@ app.post('/api/orders/:id/comments', async (req, res) => {
     }
 
     // Get current comments or initialize empty array
-    const currentComments = checkResult.rows[0].comments || [];
+    let currentComments = [];
+    try {
+      // Try to parse comments if they exist
+      if (checkResult.rows[0].comments) {
+        if (typeof checkResult.rows[0].comments === 'string') {
+          // If comments is a string, parse it
+          currentComments = JSON.parse(checkResult.rows[0].comments);
+        } else {
+          // If comments is already an object (JSONB), use it directly
+          currentComments = checkResult.rows[0].comments;
+        }
+      }
+    } catch (parseError) {
+      console.error('Error parsing comments:', parseError);
+      // If parsing fails, use empty array
+      currentComments = [];
+    }
 
     // Create new comment
     const newComment = {
@@ -595,7 +685,26 @@ app.get('/api/orders/:id/comments', async (req, res) => {
       return res.status(404).json({ error: 'Order not found' });
     }
 
-    res.json(result.rows[0].comments || []);
+    // Parse comments or return empty array
+    let comments = [];
+    try {
+      // Try to parse comments if they exist
+      if (result.rows[0].comments) {
+        if (typeof result.rows[0].comments === 'string') {
+          // If comments is a string, parse it
+          comments = JSON.parse(result.rows[0].comments);
+        } else {
+          // If comments is already an object (JSONB), use it directly
+          comments = result.rows[0].comments;
+        }
+      }
+    } catch (parseError) {
+      console.error('Error parsing comments:', parseError);
+      // If parsing fails, use empty array
+      comments = [];
+    }
+
+    res.json(comments);
   } catch (error) {
     console.error('Error fetching comments:', error);
     res.status(500).json({ error: 'Failed to fetch comments' });
-- 
2.47.0.windows.1

