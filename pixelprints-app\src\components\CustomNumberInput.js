import React from 'react';
import './CustomNumberInput.css';

const CustomNumberInput = ({ value, onChange, min = 1, max = 100, label = '' }) => {
  const handleIncrement = () => {
    const newValue = Math.min(max, parseInt(value, 10) + 1);
    onChange(newValue);
  };

  const handleDecrement = () => {
    const newValue = Math.max(min, parseInt(value, 10) - 1);
    onChange(newValue);
  };

  const handleInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue === '') {
      onChange(min);
    } else {
      const numValue = parseInt(inputValue, 10);
      if (!isNaN(numValue)) {
        const boundedValue = Math.max(min, Math.min(max, numValue));
        onChange(boundedValue);
      }
    }
  };

  return (
    <div className="custom-number-input">
      {label && <label className="number-input-label">{label}</label>}
      <div className="number-input-container">
        <button 
          type="button" 
          className="number-btn decrement-btn" 
          onClick={handleDecrement}
          aria-label="Decrease value"
          disabled={value <= min}
        >
          -
        </button>
        <input
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={value}
          onChange={handleInputChange}
          className="number-input"
          aria-label={label || "Number input"}
        />
        <button 
          type="button" 
          className="number-btn increment-btn" 
          onClick={handleIncrement}
          aria-label="Increase value"
          disabled={value >= max}
        >
          +
        </button>
      </div>
    </div>
  );
};

export default CustomNumberInput;
