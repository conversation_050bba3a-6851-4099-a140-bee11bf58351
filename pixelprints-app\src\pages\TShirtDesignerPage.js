import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TShirtDesigner from '../components/TShirtDesigner/TShirtDesigner';
import useDocumentTitle from '../hooks/useDocumentTitle';
import '../styles/tshirt-designer.css';

const TShirtDesignerPage = () => {
  const navigate = useNavigate();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userName, setUserName] = useState('');

  // Set document title
  useDocumentTitle('T-Shirt Designer | Pixel Prints');

  // Check for authentication
  useEffect(() => {
    const storedAuth = localStorage.getItem('isAuthenticated');
    const storedName = localStorage.getItem('userName');

    if (storedAuth === 'true' && storedName) {
      setIsAuthenticated(true);
      setUserName(storedName);
    }
  }, []);

  // Handle back to main
  const handleBackToMain = () => {
    navigate('/');
  };

  return (
    <div className="tshirt-designer-page">
      <div className="designer-header">
        <h1>T-Shirt Designer</h1>
        <p>Create your custom t-shirt design</p>
        {isAuthenticated ? (
          <p className="welcome-message">Welcome, {userName}</p>
        ) : (
          <p className="login-prompt">
            Please <button onClick={() => navigate('/')}>login</button> to save your designs
          </p>
        )}
        <button className="back-button" onClick={handleBackToMain}>
          Back to Main
        </button>
      </div>

      <TShirtDesigner isAuthenticated={isAuthenticated} />

      <div className="designer-footer">
        <p>© {new Date().getFullYear()} Pixel Prints. All rights reserved.</p>
      </div>
    </div>
  );
};

export default TShirtDesignerPage;
