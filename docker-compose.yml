version: '3.8'

services:
  frontend:
    build:
      context: ./pixelprints-app
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://localhost:3001

  backend:
    build:
      context: ./pixel-prints-backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - FRONTEND_URL=http://localhost:80
      - JWT_SECRET=pixel-prints-secure-jwt-key-2024
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=admin123
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=rltvbslrmehnrtyj
      - DATABASE_URL=************************************/pixel_prints
    depends_on:
      - db
    volumes:
      - ./pixel-prints-backend/uploads:/app/uploads
    restart: unless-stopped

  db:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=pixel_prints
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
