# PostgreSQL Command Line Reference

## Connecting to the Database
```bash
psql -U your_username -d your_database_name
```

## Basic Commands
- List all databases: `\l`
- Connect to a database: `\c database_name`
- List all tables: `\dt`
- Describe a table: `\d table_name`
- List all schemas: `\dn`
- List all users: `\du`
- Show command history: `\s`
- Execute commands from a file: `\i filename`
- Quit psql: `\q`

## Useful SQL Queries
- List all tables:
  ```sql
  SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
  ```

- Show table columns:
  ```sql
  SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'table_name';
  ```

- Count records in a table:
  ```sql
  SELECT COUNT(*) FROM table_name;
  ```

- Show sample data:
  ```sql
  SELECT * FROM table_name LIMIT 10;
  ```

- Find specific records:
  ```sql
  SELECT * FROM table_name WHERE column_name = 'value';
  ```

- Show database size:
  ```sql
  SELECT pg_size_pretty(pg_database_size('your_database_name'));
  ```

- Show table size:
  ```sql
  SELECT pg_size_pretty(pg_total_relation_size('table_name'));
  ```
