/**
 * Console Override
 * 
 * This file overrides the global console object to ensure that sensitive information
 * is not logged to the browser console, even when direct console calls are used.
 * 
 * It should be imported at the application entry point (index.js).
 */

import logger from './logger';

// Store original console methods
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug
};

// Environment check
const isDevelopment = process.env.NODE_ENV === 'development';

// Override console methods in production
if (!isDevelopment) {
  // Override console.log
  console.log = function(...args) {
    // Use our logger's info method which will apply filtering in production
    logger.info(...args);
  };

  // Override console.error
  console.error = function(...args) {
    // Use our logger's error method which will apply filtering in production
    logger.error(...args);
  };

  // Override console.warn
  console.warn = function(...args) {
    // Use our logger's warn method which will apply filtering in production
    logger.warn(...args);
  };

  // Override console.info
  console.info = function(...args) {
    // Use our logger's info method which will apply filtering in production
    logger.info(...args);
  };

  // Override console.debug
  console.debug = function(...args) {
    // Use our logger's debug method which will apply filtering in production
    logger.debug(...args);
  };
}

// Export the original console methods in case they're needed
export const originalConsoleLog = originalConsole.log;
export const originalConsoleError = originalConsole.error;
export const originalConsoleWarn = originalConsole.warn;
export const originalConsoleInfo = originalConsole.info;
export const originalConsoleDebug = originalConsole.debug;

// Export a function to restore the original console
export const restoreConsole = () => {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
};

export default {
  originalConsole,
  restoreConsole
};
