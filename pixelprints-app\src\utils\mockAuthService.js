/**
 * Mock Authentication Service for Development
 * 
 * This file provides mock authentication functionality for development purposes only.
 * It simulates API responses without requiring a backend server.
 */

import logger from './logger';

// Mock user database
const mockUsers = [
  {
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user'
  },
  {
    id: 2,
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  }
];

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Mock login function
 */
export const mockLogin = async (email, password) => {
  // Simulate network delay
  await delay(800);
  
  // Find user
  const user = mockUsers.find(u => u.email === email);
  
  // Check if user exists and password matches
  if (!user || user.password !== password) {
    throw new Error('Invalid credentials');
  }
  
  // Return successful response
  return {
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
    },
    token: 'mock-jwt-token-' + Date.now()
  };
};

/**
 * Mock signup function
 */
export const mockSignup = async (name, email, password) => {
  // Simulate network delay
  await delay(800);
  
  // Check if user already exists
  if (mockUsers.some(u => u.email === email)) {
    throw new Error('User with this email already exists');
  }
  
  // Create new user
  const newUser = {
    id: mockUsers.length + 1,
    name,
    email,
    password,
    role: 'user'
  };
  
  // Add to mock database
  mockUsers.push(newUser);
  
  // Log for development
  logger.info('Created mock user:', { name, email, role: 'user' });
  
  // Return successful response
  return {
    user: {
      id: newUser.id,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role
    },
    token: 'mock-jwt-token-' + Date.now()
  };
};

/**
 * Mock forgot password function
 */
export const mockForgotPassword = async (email) => {
  // Simulate network delay
  await delay(800);
  
  // Check if user exists
  const user = mockUsers.find(u => u.email === email);
  if (!user) {
    throw new Error('No account found with this email');
  }
  
  // Log for development
  logger.info('Password reset requested for:', email);
  
  // Return successful response
  return {
    message: 'Password reset link has been sent to your email'
  };
};

/**
 * Mock reset password function
 */
export const mockResetPassword = async (token, password) => {
  // Simulate network delay
  await delay(800);
  
  // Validate token (in a real app, this would decode and verify the token)
  if (!token || token.length < 10) {
    throw new Error('Invalid or expired token');
  }
  
  // Log for development
  logger.info('Password reset successful for token:', token);
  
  // Return successful response
  return {
    message: 'Your password has been reset successfully'
  };
};

export default {
  mockLogin,
  mockSignup,
  mockForgotPassword,
  mockResetPassword
};
