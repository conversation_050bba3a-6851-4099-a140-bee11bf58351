<!DOCTYPE html>
<html>
<head>
    <title>Create Pixel Prints Logo</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f5f5;
            margin: 0;
        }
        canvas {
            border: 1px solid #ddd;
        }
        .controls {
            margin-top: 20px;
            text-align: center;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div>
        <canvas id="logoCanvas" width="500" height="300"></canvas>
        <div class="controls">
            <button id="downloadBtn">Download Logo</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('logoCanvas');
        const ctx = canvas.getContext('2d');
        
        // Clear canvas with light gray background
        ctx.fillStyle = '#f5f5f5';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Draw printer icon
        ctx.fillStyle = '#5da9e9'; // Blue color for printer body
        ctx.fillRect(150, 100, 200, 80); // Printer body
        
        // Printer top
        ctx.fillStyle = '#6c757d'; // Gray color for printer top
        ctx.fillRect(180, 70, 140, 30); // Printer top
        
        // Paper tray
        ctx.fillStyle = '#e9ecef'; // Light gray for paper
        ctx.fillRect(180, 70, 140, 10); // Paper
        
        // Printer details
        ctx.fillStyle = '#343a40'; // Dark color for details
        ctx.fillRect(180, 200, 140, 30); // Bottom part
        
        // Printer button
        ctx.fillStyle = '#ffffff'; // White for button
        ctx.beginPath();
        ctx.arc(320, 130, 8, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw lines on the bottom part (paper output)
        ctx.fillStyle = '#adb5bd'; // Gray for lines
        ctx.fillRect(200, 210, 100, 5);
        ctx.fillRect(200, 220, 100, 5);
        
        // Add text "PIXEL PRINTS"
        ctx.fillStyle = '#000000'; // Black color for text
        ctx.font = 'bold 36px monospace';
        ctx.textAlign = 'center';
        ctx.fillText('PIXEL PRINTS', 250, 270);
        
        // Download functionality
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const link = document.createElement('a');
            link.download = 'PIXEL_PRINTS.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });
    </script>
</body>
</html>
