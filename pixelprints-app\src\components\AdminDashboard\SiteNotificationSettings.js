import React, { useState, useEffect } from 'react';

const SiteNotificationSettings = ({ settings, updateSiteNotification }) => {
  const [message, setMessage] = useState('');
  const [enabled, setEnabled] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');

  // Initialize form with current settings
  useEffect(() => {
    if (settings['site-notification-message']) {
      setMessage(settings['site-notification-message']);
    }

    if (settings['site-notification-enabled'] !== undefined) {
      setEnabled(settings['site-notification-enabled'] === 'true');
    }
  }, [settings]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setStatusMessage('');

    try {
      await updateSiteNotification(message, enabled);
      setStatusMessage('Site notification settings updated successfully!');
    } catch (error) {
      setStatusMessage('Failed to update settings. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="site-notification-settings">
      <h2>Site Notification Settings</h2>
      <p className="settings-description">
        Configure a notification message that will appear at the top of the main page.
        Use this for important announcements like site maintenance or service disruptions.
      </p>

      <form className="settings-form" onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="notification-message">Notification Message:</label>
          <textarea
            id="notification-message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Enter notification message"
            rows={3}
            required
          />
        </div>

        <div className="form-group">
          <div className="checkbox-container">
            <input
              id="notification-enabled"
              type="checkbox"
              checked={enabled}
              onChange={(e) => setEnabled(e.target.checked)}
            />
            <label htmlFor="notification-enabled">Enable Site Notification</label>
          </div>
        </div>

        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Updating...' : 'Save Settings'}
        </button>

        {statusMessage && <p className="message">{statusMessage}</p>}
      </form>

      <div className="notification-preview">
        <h3>Preview</h3>
        {enabled ? (
          <div className="site-notification-preview">
            <p>{message}</p>
          </div>
        ) : (
          <p className="notification-disabled">Notification is currently disabled</p>
        )}
      </div>
    </div>
  );
};

export default SiteNotificationSettings;
