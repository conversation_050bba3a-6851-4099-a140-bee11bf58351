# Deploying PixelPrints Backend to Render

This guide provides step-by-step instructions for deploying the PixelPrints backend to Render.

## Prerequisites

- A Render account (sign up at https://render.com if you don't have one)
- Git repository with your PixelPrints app code

## Deployment Steps

### 1. Log in to Render Dashboard

Go to https://dashboard.render.com and log in to your account.

### 2. Create a New PostgreSQL Database

1. Click on "New" and select "PostgreSQL"
2. Fill in the following details:
   - Name: `pixel-prints-db`
   - Database: `pixel_prints`
   - User: `pixel_prints_user`
   - Region: Choose the region closest to your users
   - PostgreSQL Version: 14
3. Click "Create Database"
4. Once created, note the "Internal Database URL" - you'll need this for your web service

### 3. Create a New Web Service

1. Click on "New" and select "Web Service"
2. Connect your GitHub repository or use the "Deploy from Git Repository" option
3. Fill in the following details:
   - Name: `pixel-prints-backend`
   - Root Directory: `pixel-prints-backend` (if your backend is in a subdirectory)
   - Environment: `Docker`
   - Branch: `main` (or your preferred branch)
   - Region: Choose the same region as your database
   - Plan: Free

4. Add the following environment variables:
   - `NODE_ENV`: `production`
   - `PORT`: `3001`
   - `FRONTEND_URL`: Your frontend URL (e.g., `https://jocular-jelly-4e9cef.netlify.app`)
   - `JWT_SECRET`: `pixel-prints-secure-jwt-key-2024` (use a more secure value in production)
   - `ADMIN_EMAIL`: `<EMAIL>`
   - `ADMIN_PASSWORD`: `admin123` (use a more secure password in production)
   - `EMAIL_USER`: `<EMAIL>`
   - `EMAIL_PASS`: Your email app password
   - `EMAIL_HOST`: `smtp.gmail.com`
   - `EMAIL_PORT`: `587`
   - `DATABASE_URL`: Use the Internal Database URL from your PostgreSQL database

5. Under "Health Check Path", enter: `/api/test`

6. Click "Create Web Service"

### 4. Initialize the Database

After your service is deployed, you'll need to initialize the database. You can do this by running the database initialization scripts.

1. Go to your web service in the Render dashboard
2. Click on "Shell"
3. Run the following commands:
   ```
   node db/init.js
   node db/setup_settings.js
   node db/create_admin.js
   node db/add_site_notification_settings.js
   node db/update_users_reset_token.js
   ```

### 5. Test Your Deployment

1. Visit your web service URL + `/api/test` (e.g., `https://pixel-prints-backend.onrender.com/api/test`)
2. You should see a JSON response: `{"message":"Backend is working!"}`

### 6. Update Your Frontend

Update your frontend application to use the new backend URL:

1. Set the `REACT_APP_API_URL` environment variable to your Render web service URL
2. Redeploy your frontend application

## Troubleshooting

If you encounter any issues:

1. Check the logs in the Render dashboard
2. Verify that all environment variables are set correctly
3. Make sure your database is properly initialized
4. Check that your frontend is using the correct backend URL

## Next Steps

- Set up automatic database backups in Render
- Configure a custom domain for your backend API
- Implement proper authentication for production use
