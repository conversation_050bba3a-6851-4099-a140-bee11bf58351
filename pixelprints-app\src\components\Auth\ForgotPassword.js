import { useState, useRef, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import "./Auth.css"
import { getApiUrl } from "../../utils/api"
import logger from "../../utils/logger"
import { mockForgotPassword } from "../../utils/mockAuthService"

const ForgotPassword = () => {
  const navigate = useNavigate()
  const [email, setEmail] = useState("")
  const [message, setMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [formAnimation, setFormAnimation] = useState("")

  // Refs for form elements
  const emailInputRef = useRef(null)
  const formRef = useRef(null)

  // Focus email input on component mount
  useEffect(() => {
    if (emailInputRef.current) {
      emailInputRef.current.focus()
    }
  }, [])

  const handleSubmit = async (e) => {
    e.preventDefault()
    setMessage("")
    setIsLoading(true)

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setMessage("Please enter a valid email address.")
      setIsLoading(false)
      return
    }

    try {
      // Determine if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';
      let data;

      if (isDevelopment) {
        // Use mock service in development mode
        logger.info('Using mock forgot password service in development mode');
        data = await mockForgotPassword(email);
      } else {
        // In production, use the real API
        const response = await fetch(getApiUrl('/api/forgot-password'), {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email }),
        })

        data = await response.json()

        if (!response.ok) {
          throw new Error(data.message || "An error occurred. Please try again.")
        }
      }

      // Add animation before showing success message
      setFormAnimation("form-fade-out")

      setTimeout(() => {
        setIsSuccess(true)
        setMessage(data.message || "Password reset link has been sent to your email.")
        setFormAnimation("form-fade-in")

        setTimeout(() => {
          setFormAnimation("")
        }, 300)
      }, 200)
    } catch (error) {
      logger.error("Forgot password error:", error)
      setMessage("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle navigation with animation
  const handleNavigate = (path) => {
    setFormAnimation("form-fade-out")

    setTimeout(() => {
      navigate(path)
    }, 200)
  }

  return (
    <div className="auth-page">
      <div className={`auth-container ${formAnimation}`}>
        <div className="auth-header">
          <h1>Forgot Password</h1>
          <p>Reset your password</p>
        </div>

        {isSuccess ? (
          <>
            <div className="success-message">
              <p>{message}</p>
            </div>
            <button
              type="button"
              className="auth-button"
              onClick={() => handleNavigate("/")}
            >
              Back to Home
            </button>
          </>
        ) : (
          <>
            <p>Enter your email address and we'll send you a link to reset your password.</p>

            {message && <div className={isSuccess ? "success-message" : "error-message"}>{message}</div>}

            <form ref={formRef} onSubmit={handleSubmit} className="auth-form">
              <div className="form-group">
                <label htmlFor="email">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  ref={emailInputRef}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  placeholder="Enter your email address"
                />
              </div>

              <button type="submit" className="auth-button" disabled={isLoading}>
                {isLoading ? "Sending..." : "Send Reset Link"}
              </button>
            </form>

            <div className="auth-footer">
              <button
                type="button"
                className="switch-button"
                onClick={() => handleNavigate("/")}
              >
                Back to Home
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ForgotPassword
