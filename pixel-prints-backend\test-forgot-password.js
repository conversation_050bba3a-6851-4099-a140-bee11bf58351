const axios = require('axios');

// Test data
const testEmail = '<EMAIL>';
const testPassword = 'newpassword123';
const testToken = 'test-reset-token';

// Base URL for API
const API_URL = 'http://localhost:3001/api';

// Test functions
async function testForgotPassword() {
  console.log('Testing Forgot Password API...');
  
  try {
    // Test 1: Request password reset
    console.log('\nTest 1: Request password reset');
    const forgotResponse = await axios.post(`${API_URL}/forgot-password`, {
      email: testEmail
    });
    
    console.log('Response:', forgotResponse.data);
    console.log('Status:', forgotResponse.status);
    console.log('Test 1 Result:', forgotResponse.status === 200 ? 'PASSED' : 'FAILED');
    
    // Test 2: Reset password with token
    console.log('\nTest 2: Reset password with token');
    const resetResponse = await axios.post(`${API_URL}/reset-password`, {
      token: testToken,
      password: testPassword
    });
    
    console.log('Response:', resetResponse.data);
    console.log('Status:', resetResponse.status);
    console.log('Test 2 Result:', resetResponse.status === 200 ? 'PASSED' : 'FAILED');
    
    console.log('\nAll tests completed!');
  } catch (error) {
    console.error('Test failed with error:', error.response ? error.response.data : error.message);
  }
}

// Run tests
testForgotPassword();
