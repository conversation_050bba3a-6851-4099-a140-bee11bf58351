const express = require('express');
const router = express.Router();
const { adminLogin, createAdmin, adminForgotPassword, adminResetPassword } = require('../controllers/admin-auth');
const { authenticateToken, isAdmin } = require('../middleware/auth');

// Admin auth routes
router.post('/login', adminLogin);

// Admin password reset routes
router.post('/forgot-password', adminForgotPassword);
router.post('/reset-password', adminResetPassword);

// Protected route to create another admin (only existing admins can create new admins)
router.post('/create', authenticateToken, isAdmin, createAdmin);

// Public route to create the first admin (should be disabled in production)
router.post('/setup', createAdmin);

module.exports = router;
