import React, { useEffect, useState } from 'react';

const TShirtPreview = ({ 
  canvas, 
  tshirtColor, 
  setTshirtColor,
  tshirtSize,
  setTshirtSize
}) => {
  const [previewImage, setPreviewImage] = useState(null);
  
  // Available t-shirt colors
  const tshirtColors = [
    { name: 'White', value: '#ffffff' },
    { name: 'Black', value: '#000000' },
    { name: 'Navy', value: '#000080' },
    { name: 'Red', value: '#ff0000' },
    { name: 'Green', value: '#008000' },
    { name: 'Yellow', value: '#ffff00' },
    { name: 'Purple', value: '#800080' },
    { name: '<PERSON>', value: '#808080' },
  ];
  
  // Available t-shirt sizes
  const tshirtSizes = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL'];
  
  // Update preview when canvas changes
  useEffect(() => {
    if (!canvas) return;
    
    // Update preview when canvas is modified
    const updatePreview = () => {
      const dataUrl = canvas.toDataURL({
        format: 'png',
        quality: 1,
      });
      setPreviewImage(dataUrl);
    };
    
    // Add event listener for canvas changes
    canvas.on('object:modified', updatePreview);
    canvas.on('object:added', updatePreview);
    canvas.on('object:removed', updatePreview);
    canvas.on('path:created', updatePreview);
    
    // Initial preview
    updatePreview();
    
    // Cleanup
    return () => {
      canvas.off('object:modified', updatePreview);
      canvas.off('object:added', updatePreview);
      canvas.off('object:removed', updatePreview);
      canvas.off('path:created', updatePreview);
    };
  }, [canvas]);
  
  return (
    <div className="tshirt-preview">
      <h3>T-Shirt Preview</h3>
      
      <div 
        className="tshirt-mockup"
        style={{ backgroundColor: tshirtColor }}
      >
        {/* T-shirt shape */}
        <div className="tshirt-shape">
          {/* Design placement area */}
          <div className="design-placement">
            {previewImage && (
              <img 
                src={previewImage} 
                alt="Your design" 
                className="design-preview-image"
              />
            )}
          </div>
        </div>
      </div>
      
      <div className="tshirt-options">
        <div className="color-options">
          <label>T-Shirt Color:</label>
          <div className="color-swatches">
            {tshirtColors.map((color) => (
              <div
                key={color.value}
                className={`color-swatch ${tshirtColor === color.value ? 'selected' : ''}`}
                style={{ backgroundColor: color.value }}
                onClick={() => setTshirtColor(color.value)}
                title={color.name}
              />
            ))}
          </div>
        </div>
        
        <div className="size-options">
          <label>Size:</label>
          <div className="size-buttons">
            {tshirtSizes.map((size) => (
              <button
                key={size}
                className={`size-button ${tshirtSize === size ? 'selected' : ''}`}
                onClick={() => setTshirtSize(size)}
              >
                {size}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      <div className="preview-tips">
        <p>Your design will be printed exactly as shown on the preview.</p>
        <p>For best results, use high-resolution images.</p>
      </div>
    </div>
  );
};

export default TShirtPreview;
