# Netlify Configuration Update Guide

To ensure your Netlify frontend at https://pixelprints.it.com properly connects with your Railway backend, follow these steps:

## 1. Log in to Netlify Dashboard

Go to [Netlify Dashboard](https://app.netlify.com/) and log in to your account.

## 2. Select Your Site

Find and select your `pixelprints-app` site.

## 3. Navigate to Site Settings

Click on "Site settings" in the top navigation.

## 4. Go to Environment Variables

In the left sidebar, click on "Environment variables".

## 5. Update Environment Variables

Make sure you have the following environment variable set:

```
REACT_APP_API_URL=https://pixel-prints-backend-production.up.railway.app
```

Replace `pixel-prints-backend-production.up.railway.app` with your actual Railway app URL if it's different.

## 6. Save Changes

Click the "Save" button to apply your changes.

## 7. Trigger a New Deployment

Go to the "Deploys" tab and click "Trigger deploy" > "Deploy site" to apply the changes.

## 8. Update Content Security Policy

If you're experiencing any issues with API connections, you may need to update your Content Security Policy in your `netlify.toml` file:

```toml
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self'; connect-src 'self' https://*.railway.app https://pixel-prints-backend-production.up.railway.app; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval';"
```

## 9. Test Admin Login

Once the deployment is complete, try logging in to the admin dashboard at https://pixelprints.it.com/admin-login with:

- Email: <EMAIL>
- Password: admin123

## Troubleshooting

If you're still experiencing issues:

1. Open your browser's developer tools (F12)
2. Go to the Network tab
3. Try logging in and look for any failed requests
4. Check the Console tab for any error messages

Common issues:
- CORS errors: Make sure your Railway backend has the correct FRONTEND_URL
- Authentication errors: Make sure your admin credentials are correct
- Connection errors: Make sure your REACT_APP_API_URL is correct
