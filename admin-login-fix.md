# Admin Login Fix

To fix the admin login issue, make the following changes:

## 1. In `pixelprints-app/src/utils/api.js`:

Remove the `credentials: 'include'` line from the fetchOptions object:

```javascript
// Add CORS mode
const fetchOptions = {
  ...options,
  mode: 'cors',
  // Removed credentials: 'include' which can cause CORS issues
  headers: {
    ...options.headers,
    'Content-Type': 'application/json',
  }
};
```

## 2. In `pixelprints-app/src/pages/AdminLoginPage.js`:

Change the error message display to show the actual error message:

```javascript
{error && <div className="error-message">
  <p>{error}</p>
</div>}
```

These changes will fix the CORS issues that are preventing successful admin login.
