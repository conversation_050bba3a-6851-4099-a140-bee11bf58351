import { useNavigate } from 'react-router-dom';
import Logo from './Logo';

function Header({ isAuthenticated, userName, setIsAuthenticated, setUserName, setShowAuthModal }) {
  const navigate = useNavigate();

  return (
    <header className="App-header">
      <div className="header-content">
        <Logo />
        <div className="slogan">Knock, knock! Who's there? Your Prints</div>
        <div className="user-info">
          {isAuthenticated ? (
            <>
              <span>Welcome, {userName}</span>
              <button
                onClick={() => {
                  setIsAuthenticated(false);
                  setUserName("Guest");
                  localStorage.removeItem("isAuthenticated");
                  localStorage.removeItem("userName");
                }}
                className="logout-button"
              >
                Logout
              </button>
              <button
                onClick={() => navigate('/dashboard')}
                className="dashboard-button"
              >
                My Dashboard
              </button>
            </>
          ) : (
            <button onClick={() => setShowAuthModal(true)} className="login-button">
              Sign Up / Login
            </button>
          )}
        </div>
      </div>
    </header>
  );
}

export default Header;