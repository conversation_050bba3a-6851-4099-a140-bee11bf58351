# Pixel Prints Proxy Server

A simple CORS proxy server for the Pixel Prints application.

## Description

This proxy server allows the Pixel Prints frontend to communicate with the backend API without CORS issues. It forwards all requests to the target API and returns the responses with the appropriate CORS headers.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Start the server:
   ```
   npm start
   ```

## Deployment

This proxy server can be deployed to services like Render, Heroku, or Vercel.

### Render Deployment

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Set the following:
   - Build Command: `npm install`
   - Start Command: `npm start`
   - Environment Variables: None required

## Usage

Once deployed, update the frontend API URL to point to this proxy server instead of directly to the backend API.

Example:
```
REACT_APP_API_URL=https://your-proxy-server-url.com
```
