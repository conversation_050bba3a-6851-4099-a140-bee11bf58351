import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getApiUrl } from '../utils/api';
import './AdminLoginPage.css';

const AdminForgotPassword = ({ onBackToMain }) => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');
    setIsLoading(true);

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setMessage('Please enter a valid email address.');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(getApiUrl('/api/admin/forgot-password'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSuccess(true);
        setMessage(data.message || 'If your email is registered as an admin, you will receive a password reset link.');
      } else {
        setMessage(data.message || 'An error occurred. Please try again.');
      }
    } catch (error) {
      console.error('Admin forgot password error:', error);
      setMessage('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-login-page">
      <div className="admin-login-container">
        <div className="admin-login-header">
          <h1>Admin Password Reset</h1>
          <button className="back-button" onClick={onBackToMain}>
            Back to Main
          </button>
        </div>

        {isSuccess ? (
          <>
            <div className="success-message">
              <p>{message}</p>
            </div>
            <button
              type="button"
              className="admin-login-button"
              onClick={() => navigate('/admin-login')}
            >
              Back to Admin Login
            </button>
          </>
        ) : (
          <>
            <p>Enter your admin email address and we'll send you a link to reset your password.</p>

            {message && <div className={isSuccess ? 'success-message' : 'error-message'}>
              <p>{message}</p>
            </div>}

            <form className="admin-login-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="admin-email">Admin Email</label>
                <input
                  id="admin-email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <button
                type="submit"
                className="admin-login-button"
                disabled={isLoading}
              >
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </button>
            </form>

            <div className="admin-login-footer">
              <button
                type="button"
                className="switch-button"
                onClick={() => navigate('/admin-login')}
              >
                Back to Admin Login
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminForgotPassword;
