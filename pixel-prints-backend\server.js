
const express = require("express")
const multer = require("multer")
const nodemailer = require("nodemailer")
const cors = require("cors")
const fs = require("fs")
const path = require("path")
const bcrypt = require("bcryptjs")
require("dotenv").config()
const db = require("./db")

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads')
if (!fs.existsSync(uploadsDir)) {
  console.log('Creating uploads directory:', uploadsDir)
  fs.mkdirSync(uploadsDir, { recursive: true })
}

// Import routes
const settingsRoutes = require('./src/routes/settings');
const adminAuthRoutes = require('./src/routes/admin-auth');
const usersRoutes = require('./src/routes/users');
const authRoutes = require('./src/routes/auth-routes');

const app = express()

// Middleware
app.use(cors({
  origin: function(origin, callback) {
    // Always include these domains
    const allowedOrigins = [
      'https://pixel-prints.netlify.app',
      'https://pixelprints.it.com',
      'http://localhost:3000'
    ];

    // Add FRONTEND_URL from environment variable if it exists
    if (process.env.FRONTEND_URL) {
      // Check if it's a comma-separated list
      if (process.env.FRONTEND_URL.includes(',')) {
        // Split and add each URL
        const envUrls = process.env.FRONTEND_URL.split(',').map(url => url.trim());
        allowedOrigins.push(...envUrls);
      } else {
        // Add single URL
        allowedOrigins.push(process.env.FRONTEND_URL);
      }
    }

    // Remove duplicates
    const uniqueOrigins = [...new Set(allowedOrigins)];

    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin || uniqueOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin, 'Allowed origins:', uniqueOrigins);
      callback(null, false);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}))
app.use(express.json())

// Register routes
app.use('/api/settings', settingsRoutes);
app.use('/api/admin', adminAuthRoutes);
app.use('/api/users', usersRoutes);
app.use('/api', authRoutes);

// Multer configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir)
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + "-" + file.originalname)
  },
})

const fileFilter = (req, file, cb) => {
  if (file.mimetype === "application/pdf" || file.mimetype === "image/jpeg" || file.mimetype === "image/jpg") {
    cb(null, true)
  } else {
    cb(new Error("Invalid file type"), false)
  }
}

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024,
    files: 10,
  },
})

// Email transporter setup
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
})



// Custom error handler for the send-order route
const handleOrderUpload = (req, res, next) => {
  upload.array("files", 10)(req, res, (err) => {
    if (err) {
      console.error("File upload error:", err);
      return res.status(400).json({
        message: `File upload error: ${err.message}`,
        error: err.name || 'UploadError'
      });
    }
    next();
  });
};

app.post("/send-order", handleOrderUpload, async (req, res) => {
  try {
    console.log("Received order:", req.body);

    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      console.log("No files were uploaded");
      // Continue processing as this might be intentional
    }

    // Check if daily order limit is reached
    // Get the daily order limit setting
    try {
      const limitResult = await db.query('SELECT value FROM settings WHERE key = $1', ['daily_order_limit']);
      const enabledResult = await db.query('SELECT value FROM settings WHERE key = $1', ['daily_order_limit_enabled']);

      if (limitResult.rows.length > 0 && enabledResult.rows.length > 0) {
        const dailyOrderLimit = parseInt(limitResult.rows[0].value, 10);
        const isEnabled = enabledResult.rows[0].value === 'true';

        if (isEnabled) {
          // Get the count of orders for today
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          const orderCountResult = await db.query(
            'SELECT COUNT(*) FROM orders WHERE created_at >= $1',
            [today]
          );

          const ordersToday = parseInt(orderCountResult.rows[0].count, 10);

          if (ordersToday >= dailyOrderLimit) {
            return res.status(429).json({
              message: `Daily order limit of ${dailyOrderLimit} orders has been reached. Please try again tomorrow.`,
              limitReached: true,
              ordersToday: ordersToday,
              limit: dailyOrderLimit
            });
          }
        }
      }
    } catch (dbError) {
      console.error("Error checking daily order limit:", dbError);
      // Continue processing even if checking the limit fails
    }

    const {
      orderNumber,
      customerName,
      customerEmail,
      printType,
      totalPages,
      price,
      address,
      province,
      deliveryCharge
    } = req.body;

    // Process files information
    const filesInfo = req.files ? req.files.map((file, index) => {
      const fileInfoKey = `fileInfo${index}`;
      return req.body[fileInfoKey] ? JSON.parse(req.body[fileInfoKey]) : null;
    }).filter(Boolean) : [];

    // Database query
    const query = `
      INSERT INTO orders (
        order_number,
        customer_name,
        customer_email,
        print_type,
        total_pages,
        price,
        address,
        province,
        delivery_charge,
        files
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id`;

    const values = [
      orderNumber,
      customerName,
      customerEmail,
      printType,
      parseInt(totalPages),
      parseFloat(price),
      address,
      province,
      parseFloat(deliveryCharge),
      JSON.stringify(filesInfo)
    ];

    const dbResult = await db.query(query, values);

    // Prepare files list for email content
    let filesList = '';
    if (filesInfo && filesInfo.length > 0) {
      filesList = `
      Files:
      ${filesInfo.map((info, index) =>
        `${index + 1}. ${info.name} (${info.pages} pages, ${info.copies} ${info.copies > 1 ? 'copies' : 'copy'})`
      ).join('\n      ')}
      `;
    }

    // Prepare email content for admin
    const adminEmailContent = `
      New Order Details:
      Order Number: ${orderNumber}
      Customer: ${customerName}
      Email: ${customerEmail}
      Print Type: ${printType}
      Total Pages: ${totalPages}
      Price: Php ${price}
      Province: ${province || 'Not specified'}
      Delivery Charge: Php ${deliveryCharge || 0}
      Delivery Address: ${address}
      ${filesList}
    `;

    // Prepare email content for customer
    const customerEmailContent = `
      Dear ${customerName},

      Thank you for your order with Pixel Prints!

      Your order has been received and is being processed. Here's a summary of your order:

      Order Number: ${orderNumber}
      Print Type: ${printType === 'black' ? 'Black & White' : 'Colored'}
      Total Price: Php ${price}
      Delivery Address: ${address}
      ${filesList}

      We will process your order as soon as possible. You will receive another notification when your order is ready for delivery.

      If you have any questions about your order, please contact us and reference your order number.

      Thank you for choosing Pixel Prints!

      Best regards,
      The Pixel Prints Team
    `;

    // Prepare attachments from uploaded files
    const attachments = req.files ? req.files.map(file => ({
      filename: file.originalname,
      path: file.path
    })) : [];

    // Send email to admin
    const adminMailOptions = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: `New Order: ${orderNumber}`,
      text: adminEmailContent,
      attachments: attachments
    };

    // Send email to customer
    const customerMailOptions = {
      from: process.env.EMAIL_USER,
      to: customerEmail,
      subject: `Your Pixel Prints Order Confirmation #${orderNumber}`,
      text: customerEmailContent,
      attachments: attachments
    };

    // Send both emails
    try {
      await Promise.all([
        transporter.sendMail(adminMailOptions),
        transporter.sendMail(customerMailOptions)
      ]);
      console.log('Emails sent successfully');
    } catch (emailError) {
      console.error('Error sending emails:', emailError);
      // Continue processing - we don't want to fail the order just because emails failed
      // But we should log this for follow-up
    }
    res.status(200).json({
      message: "Order placed successfully",
      orderNumber: orderNumber,
      orderId: dbResult.rows[0].id
    });

  } catch (error) {
    console.error("Error processing order:", error);

    // Make sure we haven't already sent a response
    if (!res.headersSent) {
      // Send a proper JSON response
      return res.status(500).json({
        message: "Failed to process order: " + error.message,
        error: error.name || 'UnknownError'
      });
    }
  }
})

// Get all orders
app.get("/api/orders", async (req, res) => {
  try {
    const result = await db.query("SELECT * FROM orders ORDER BY created_at DESC")
    res.json(result.rows)
  } catch (error) {
    console.error("Error fetching orders:", error)
    res.status(500).json({ message: "Failed to fetch orders" })
  }
})

// Get orders by customer email
app.get("/api/orders/customer/:email", async (req, res) => {
  try {
    const result = await db.query("SELECT * FROM orders WHERE customer_email = $1 ORDER BY created_at DESC", [
      req.params.email,
    ])
    res.json(result.rows)
  } catch (error) {
    console.error("Error fetching customer orders:", error)
    res.status(500).json({ message: "Failed to fetch customer orders" })
  }
})

// Get orders statistics
app.get("/api/orders/stats/summary", async (req, res) => {
  try {
    const query = `
      SELECT
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CAST(price AS DECIMAL)) as total_revenue
      FROM orders
    `;
    const result = await db.query(query);
    res.json(result.rows[0]);
  } catch (error) {
    console.error("Error fetching order statistics:", error);
    res.status(500).json({ message: "Failed to fetch order statistics" });
  }
});

// Get order by ID
app.get("/api/orders/:id", async (req, res) => {
  try {
    const result = await db.query("SELECT * FROM orders WHERE id = $1", [req.params.id])
    if (result.rows.length === 0) {
      return res.status(404).json({ message: "Order not found" })
    }
    res.json(result.rows[0])
  } catch (error) {
    console.error("Error fetching order:", error)
    res.status(500).json({ message: "Failed to fetch order" })
  }
})

// Helper function to send customer confirmation email
async function sendCustomerConfirmationEmail(
  customerName,
  customerEmail,
  orderNumber,
  printType,
  quantity,
  price,
  address,
  province,
  deliveryCharge,
  fileInfoArray,
) {
  let filesList = ""

  if (fileInfoArray && fileInfoArray.length > 0) {
    filesList = `
    Files:
    ${fileInfoArray
      .map(
        (info, index) =>
          `${index + 1}. ${info.name} (${info.pages} pages, ${info.copies} ${info.copies > 1 ? "copies" : "copy"})`,
      )
      .join("\n")}
    `
  }

  const customerEmailContent = `
    Dear ${customerName},

    Thank you for your order with Pixel Prints!

    Your order has been received and is being processed. Here's a summary of your order:

    Order Number: ${orderNumber}
    Print Type: ${printType === "black" ? "Black & White" : "Colored"}
    Province: ${province || "Not specified"}
    Delivery Charge: Php ${deliveryCharge || 0}
    Total Price: Php ${price}
    Delivery Address: ${address}
    ${filesList}

    We will process your order as soon as possible. You will receive another notification when your order is ready for delivery.

    If you have any questions about your order, please contact us and reference your order number.

    Thank you for choosing Pixel Prints!

    Best regards,
    The Pixel Prints Team
  `

  const customerMailOptions = {
    from: process.env.EMAIL_USER,
    to: customerEmail,
    subject: `Your Pixel Prints Order Confirmation #${orderNumber}`,
    text: customerEmailContent,
  }

  await transporter.sendMail(customerMailOptions)
  console.log("Confirmation email sent to customer:", customerEmail)
}

// Test route
app.get("/api/test", (req, res) => {
  res.json({ message: "Backend is working!" })
})

// Root route
app.get("/", (req, res) => {
  res.json({
    message: "PixelPrints API is running",
    endpoints: [
      "/api/test",
      "/api/orders",
      "/api/admin/login"
    ],
    documentation: "See README.md for API documentation"
  })
})

// Serve uploaded files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Ensure admin user exists
const ensureAdmin = async () => {
  try {
    console.log('Checking for admin user...');

    // Check if admin user exists
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

    console.log(`Looking for admin with email: ${adminEmail}`);

    const result = await db.query('SELECT * FROM users WHERE email = $1 AND role = $2', [adminEmail, 'admin']);

    if (result.rows.length === 0) {
      console.log('No admin user found. Creating admin user...');

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(adminPassword, salt);

      // Create admin user
      const insertResult = await db.query(
        'INSERT INTO users (name, email, password, role) VALUES ($1, $2, $3, $4) RETURNING id, name, email, role',
        ['Admin User', adminEmail, hashedPassword, 'admin']
      );

      console.log('Admin user created successfully:', insertResult.rows[0]);
    } else {
      console.log('Admin user already exists:', result.rows[0].email);
    }
  } catch (error) {
    console.error('Error ensuring admin exists:', error);
  }
};

// Ensure comments column exists in orders table
const ensureCommentsColumn = async () => {
  try {
    console.log('Checking for comments column in orders table...');

    // Check if comments column exists
    const checkResult = await db.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'orders' AND column_name = 'comments'
    `);

    if (checkResult.rows.length === 0) {
      console.log('Adding comments column to orders table...');

      // Add comments column as JSONB with default empty array
      await db.query(`
        ALTER TABLE orders
        ADD COLUMN comments JSONB DEFAULT '[]'::jsonb
      `);

      console.log('Comments column added successfully');
    } else {
      console.log('Comments column already exists in orders table');
    }
  } catch (error) {
    console.error('Error ensuring comments column exists:', error);
  }
};

// Start server
const PORT = process.env.PORT || 3001
app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`)
  console.log(`Uploads directory: ${uploadsDir}`)

  // Run database migrations
  await ensureCommentsColumn();

  // Ensure admin user exists
  await ensureAdmin();

  console.log('Server initialization completed');
})

// Add this near your other endpoints
app.get('/api/table-info', async (req, res) => {
  try {
    const result = await db.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'orders';
    `);
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/test-email', async (req, res) => {
  try {
    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER,
      subject: 'Test Email',
      text: 'This is a test email to verify the email functionality is working.'
    });
    res.send('Test email sent successfully');
  } catch (error) {
    console.error('Email test failed:', error);
    res.status(500).send(`Email test failed: ${error.message}`);
  }
});

// Add endpoint for deleting an order
app.delete('/api/orders/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // First check if the order exists
    const checkQuery = `SELECT id FROM orders WHERE id = $1`;
    const checkResult = await db.query(checkQuery, [id]);

    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Delete the order
    const deleteQuery = `DELETE FROM orders WHERE id = $1`;
    await db.query(deleteQuery, [id]);

    res.json({ message: 'Order deleted successfully' });
  } catch (error) {
    console.error('Error deleting order:', error);
    res.status(500).json({ error: 'Failed to delete order' });
  }
});

// Update order status
app.put('/api/orders/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }

    // Validate status
    const validStatuses = ['pending', 'processing', 'completed', 'delayed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        error: 'Invalid status',
        validStatuses
      });
    }

    // Check if order exists
    const checkQuery = `SELECT id FROM orders WHERE id = $1`;
    const checkResult = await db.query(checkQuery, [id]);

    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Update order status
    const updateQuery = `UPDATE orders SET status = $1 WHERE id = $2 RETURNING *`;
    const result = await db.query(updateQuery, [status, id]);

    res.json({
      message: 'Order status updated successfully',
      order: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ error: 'Failed to update order status' });
  }
});

// Add comment to order
app.post('/api/orders/:id/comments', async (req, res) => {
  try {
    const { id } = req.params;
    const { text, author } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Comment text is required' });
    }

    // Check if order exists and get current comments
    const checkQuery = `SELECT id, comments FROM orders WHERE id = $1`;
    const checkResult = await db.query(checkQuery, [id]);

    if (checkResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Get current comments or initialize empty array
    let currentComments = [];
    try {
      // Try to parse comments if they exist
      if (checkResult.rows[0].comments) {
        if (typeof checkResult.rows[0].comments === 'string') {
          // If comments is a string, parse it
          currentComments = JSON.parse(checkResult.rows[0].comments);
        } else {
          // If comments is already an object (JSONB), use it directly
          currentComments = checkResult.rows[0].comments;
        }
      }
    } catch (parseError) {
      console.error('Error parsing comments:', parseError);
      // If parsing fails, use empty array
      currentComments = [];
    }

    // Create new comment
    const newComment = {
      id: Date.now().toString(),
      text,
      author: author || 'Customer',
      timestamp: new Date().toISOString()
    };

    // Add new comment to array
    const updatedComments = [...currentComments, newComment];

    // Update order with new comments
    const updateQuery = `UPDATE orders SET comments = $1 WHERE id = $2 RETURNING *`;
    const result = await db.query(updateQuery, [JSON.stringify(updatedComments), id]);

    res.json({
      message: 'Comment added successfully',
      comment: newComment,
      order: result.rows[0]
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ error: 'Failed to add comment' });
  }
});

// Get comments for an order
app.get('/api/orders/:id/comments', async (req, res) => {
  try {
    const { id } = req.params;

    // Get order with comments
    const query = `SELECT comments FROM orders WHERE id = $1`;
    const result = await db.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Parse comments or return empty array
    let comments = [];
    try {
      // Try to parse comments if they exist
      if (result.rows[0].comments) {
        if (typeof result.rows[0].comments === 'string') {
          // If comments is a string, parse it
          comments = JSON.parse(result.rows[0].comments);
        } else {
          // If comments is already an object (JSONB), use it directly
          comments = result.rows[0].comments;
        }
      }
    } catch (parseError) {
      console.error('Error parsing comments:', parseError);
      // If parsing fails, use empty array
      comments = [];
    }

    res.json(comments);
  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({ error: 'Failed to fetch comments' });
  }
});

// Catch-all route handler for 404 errors
app.use('*', (req, res) => {
  res.status(404).json({
    message: `Route not found: ${req.originalUrl}`,
    error: 'NotFound'
  });
});

// Error handling middleware for multer errors - must be after all routes
app.use((err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // A Multer error occurred when uploading
    console.error("Multer error:", err);
    return res.status(400).json({
      message: `File upload error: ${err.message}`,
      code: err.code
    });
  } else if (err) {
    // An unknown error occurred
    console.error("Unknown error:", err);
    return res.status(500).json({
      message: `Server error: ${err.message}`
    });
  }
  next();
});

// End of file
