import { useEffect } from 'react';

/**
 * Custom hook to set the document title
 * @param {string} title - The title to set for the document
 */
const useDocumentTitle = (title) => {
  useEffect(() => {
    // Save the previous title
    const previousTitle = document.title;
    
    // Set the new title
    document.title = title;
    
    // Cleanup function to restore the previous title if needed
    return () => {
      document.title = previousTitle;
    };
  }, [title]);
};

export default useDocumentTitle;
