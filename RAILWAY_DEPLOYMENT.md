# Deploying Pixel Prints Backend to Railway

This guide explains how to deploy the Pixel Prints backend to Railway.

## Why Railway?

Railway offers several advantages:
- Free trial with $5 of usage
- PostgreSQL database support
- Automatic deployments from GitHub
- Simple CLI for deployment
- Environment variable management
- Monitoring and logs

## Prerequisites

- [Railway CLI](https://docs.railway.app/develop/cli) installed
- A Railway account
- Git repository with your Pixel Prints code

## Deployment Options

### Option 1: Using the Deployment Script

We've provided deployment scripts for both PowerShell and Bash:

#### PowerShell (Windows)

```powershell
.\deploy-to-railway.ps1
```

#### Bash (Linux/Mac)

```bash
chmod +x deploy-to-railway.sh
./deploy-to-railway.sh
```

### Option 2: Manual Deployment

If you prefer to deploy manually, follow these steps:

1. **Install Railway CLI**

```bash
npm install -g @railway/cli
```

2. **Login to Railway**

```bash
railway login
```

3. **Create a New Project**

```bash
railway init --name pixel-prints-backend
```

4. **Add PostgreSQL Database**

```bash
railway add --plugin postgresql
```

5. **Navigate to Backend Directory**

```bash
cd pixel-prints-backend
```

6. **Set Environment Variables**

```bash
railway vars set NODE_ENV=production
railway vars set FRONTEND_URL=https://jocular-jelly-4e9cef.netlify.app
railway vars set JWT_SECRET=pixel-prints-secure-jwt-key-2024
railway vars set ADMIN_EMAIL=<EMAIL>
railway vars set ADMIN_PASSWORD=admin123
railway vars set EMAIL_USER=<EMAIL>
railway vars set EMAIL_PASS=your-email-password
```

7. **Link the Directory to the Railway Project**

```bash
railway link
```

8. **Deploy the Backend**

```bash
railway up
```

9. **Initialize the Database**

```bash
railway run node db/init.js
railway run node db/setup_settings.js
railway run node db/create_admin.js
railway run node db/add_site_notification_settings.js
```

## Updating the Frontend

After deploying the backend to Railway, you need to update the frontend configuration:

1. **Get the Deployed URL**

```bash
railway status --json
```

2. **Update Frontend Environment Variables**

```bash
echo "REACT_APP_API_URL=https://your-railway-url.up.railway.app" > pixelprints-app/.env.production
```

3. **Update Content Security Policy in netlify.toml**

Edit `pixelprints-app/netlify.toml` and update the Content-Security-Policy to include your Railway domain:

```toml
Content-Security-Policy = "default-src 'self'; connect-src 'self' https://*.railway.app https://*.up.railway.app; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval';"
```

4. **Redeploy the Frontend to Netlify**

```bash
cd pixelprints-app
npm run build
netlify deploy --prod
```

## Troubleshooting

### Database Connection Issues

If you have issues connecting to the database:

1. Verify that the `DATABASE_URL` environment variable is set correctly
2. Check the Railway logs for any database connection errors
3. Make sure your IP is allowed to connect to the database

### CORS Issues

If you encounter CORS issues:

1. Verify that the `FRONTEND_URL` environment variable is set correctly
2. Check the CORS configuration in server.js
3. Update the Content Security Policy in your frontend's netlify.toml file

### Deployment Issues

If you encounter issues during deployment:

1. Check the Railway logs for any errors
2. Verify that all environment variables are set correctly
3. Make sure your project is linked to the correct Railway project

## Additional Resources

- [Railway Documentation](https://docs.railway.app/)
- [Railway CLI Documentation](https://docs.railway.app/develop/cli)
- [PostgreSQL on Railway](https://docs.railway.app/databases/postgresql)
