const { Pool } = require('pg');
require('dotenv').config();

// Try to use DATABASE_URL from environment variables first
let pool;
if (process.env.DATABASE_URL) {
  console.log('Using DATABASE_URL from environment variables');
  pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });
} else {
  // Fall back to config file
  console.log('DATABASE_URL not found, using config file');
  const dbConfig = require('./config/db.config.js');
  pool = new Pool({
    user: dbConfig.USER,
    host: dbConfig.HOST,
    database: dbConfig.DB,
    password: dbConfig.PASSWORD,
    port: dbConfig.port
  });
}

async function checkDatabaseConnection() {
  try {
    console.log('Attempting to connect to database...');
    const client = await pool.connect();
    console.log('Database connection successful!');
    
    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'users'
      );
    `);
    
    if (tableCheck.rows[0].exists) {
      console.log('Users table exists.');
      
      // Check for admin user
      const adminCheck = await client.query(`
        SELECT id, name, email, role FROM users WHERE email = '<EMAIL>';
      `);
      
      if (adminCheck.rows.length > 0) {
        console.log('Admin user found:', adminCheck.rows[0]);
      } else {
        console.log('Admin user not found!');
      }
    } else {
      console.log('Users table does not exist!');
    }
    
    client.release();
  } catch (error) {
    console.error('Database connection error:', error);
  } finally {
    await pool.end();
    console.log('Database connection closed.');
  }
}

checkDatabaseConnection();
