import React from 'react';

const AdminStats = ({ stats }) => {
  return (
    <div className="stats-container">
      <div className="stat-card">
        <h3>Total Orders</h3>
        <p className="stat-value">{stats.totalOrders}</p>
      </div>
      <div className="stat-card">
        <h3>Pending Orders</h3>
        <p className="stat-value">{stats.pendingOrders}</p>
      </div>
      <div className="stat-card">
        <h3>Completed Orders</h3>
        <p className="stat-value">{stats.completedOrders}</p>
      </div>
      <div className="stat-card">
        <h3>Total Revenue</h3>
        <p className="stat-value">₱{stats.totalRevenue.toFixed(2)}</p>
      </div>
      <div className="stat-card">
        <h3>Orders Today</h3>
        <p className="stat-value">{stats.ordersToday}</p>
      </div>
    </div>
  );
};

export default AdminStats;
