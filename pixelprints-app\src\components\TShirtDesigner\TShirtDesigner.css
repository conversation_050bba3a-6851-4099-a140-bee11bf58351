/* TShirtDesigner.css */

/* Main container */
.tshirt-designer-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Tools panel */
.designer-tools-panel {
  flex: 1;
  min-width: 280px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  max-height: 800px;
  overflow-y: auto;
}

/* Canvas container */
.designer-canvas-container {
  flex: 2;
  min-width: 500px;
  min-height: 600px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* Preview panel */
.designer-preview-panel {
  flex: 1;
  min-width: 280px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 800px;
  overflow-y: auto;
}

/* Tool panel styles */
.tool-panel h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.tool-panel h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #333;
}

/* Text editor panel */
.text-editor-panel {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.text-input-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.text-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.add-text-button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-text-button:hover {
  background-color: #40a9ff;
}

.font-selector {
  margin-bottom: 15px;
}

.font-selector label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

.font-dropdown select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
}

.text-color-control {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.text-color-control label {
  min-width: 80px;
  font-size: 14px;
  color: #555;
}

.color-label {
  font-size: 14px;
  color: #555;
}

.text-size-control {
  margin-bottom: 15px;
}

.text-size-control label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

.text-size-control {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.text-size-control input {
  width: 60px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
}

.text-size-control .size-buttons {
  display: flex;
  gap: 5px;
}

.text-size-control .size-buttons button {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-size-control .size-buttons button:hover {
  background-color: #e0e0e0;
}

.rotation-control {
  margin-bottom: 15px;
}

.rotation-control label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

.rotation-control input {
  width: calc(100% - 40px);
  margin-right: 10px;
}

.delete-button {
  width: 100%;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.delete-button:hover {
  background-color: #ff7875;
}

.divider {
  height: 1px;
  background-color: #ddd;
  margin: 20px 0;
}

.clear-button {
  width: 100%;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.clear-button:hover {
  background-color: #ff7875;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 50%;
  cursor: pointer;
  padding: 0;
  background: none;
  -webkit-appearance: none;
  appearance: none;
}

/* Image uploader and editor styles */
.image-uploader {
  margin-top: 20px;
}

.image-editor-panel {
  margin: 15px 0;
  background-color: #fff;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.opacity-control {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.opacity-control label {
  min-width: 70px;
  font-size: 14px;
  color: #555;
}

.opacity-control input {
  flex: 1;
  margin: 0 10px;
}

.opacity-control span {
  min-width: 40px;
  text-align: right;
  font-size: 14px;
  color: #555;
}

.blend-mode-control {
  margin-bottom: 15px;
}

.blend-mode-control label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

.blend-mode-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.blend-mode-button {
  padding: 6px 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.blend-mode-button:hover {
  background-color: #e6f7ff;
}

.blend-mode-button.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.filter-control {
  margin-bottom: 10px;
}

.filter-control label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.filter-button {
  padding: 6px 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-button:hover {
  background-color: #e6f7ff;
}

.filter-button.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.upload-container {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-instructions {
  font-size: 14px;
  color: #666;
  margin: 10px 0 0;
}

.file-input-label {
  background-color: #1890ff;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.file-input-label:hover {
  background-color: #40a9ff;
}

.file-input {
  display: none;
}

.error-message {
  color: #f5222d;
  margin-bottom: 10px;
  font-size: 14px;
}

.image-tips {
  font-size: 12px;
  color: #666;
}

/* View options */
.view-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.view-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 5px;
  background: none;
  border: none;
  border-right: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-button:last-child {
  border-right: none;
}

.view-button img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-bottom: 5px;
}

.view-button span {
  font-size: 12px;
  color: #666;
}

.view-button.active {
  background-color: #e6f7ff;
}

.view-button.active span {
  color: #1890ff;
  font-weight: 500;
}

/* Model selector */
.model-selector {
  display: flex;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.model-button {
  flex: 1;
  padding: 10px;
  background: none;
  border: none;
  border-right: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #666;
}

.model-button:last-child {
  border-right: none;
}

.model-button.active {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

/* T-shirt preview styles */
.tshirt-preview h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.tshirt-mockup {
  position: relative;
  width: 100%;
  height: 350px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
  padding: 10px;
  box-sizing: border-box;
}

.design-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 65%;
  height: 70%;
  z-index: 10;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.design-overlay img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.design-placeholder {
  font-size: 14px;
  color: #666;
  text-align: center;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
}

.design-placement-guide {
  width: 100%;
  height: 100%;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

.tshirt-model-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tshirt-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tshirt-shape {
  width: 80%;
  height: 90%;
  background-color: inherit;
  position: relative;
  border-radius: 20px 20px 0 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #333;
  font-size: 16px;
  text-align: center;
  padding: 20px;
}

.tshirt-shape span {
  margin: 5px 0;
}

.loading-tshirt {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 14px;
  color: #666;
  text-align: center;
  padding: 20px;
}

.tshirt-shape:before,
.tshirt-shape:after {
  content: '';
  position: absolute;
  top: 0;
  width: 25%;
  height: 30%;
  background-color: inherit;
  border-radius: 50% 50% 0 0;
}

.tshirt-shape:before {
  left: -10%;
}

.tshirt-shape:after {
  right: -10%;
}

.design-placement {
  width: 60%;
  height: 60%;
  position: relative;
  top: -10%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.design-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.tshirt-options {
  margin-bottom: 20px;
}

.color-options,
.size-options {
  margin-bottom: 15px;
}

.color-swatches {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.color-swatch {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.color-swatch:hover {
  transform: scale(1.1);
}

.color-swatch.selected {
  border: 2px solid #1890ff;
  transform: scale(1.1);
}

.size-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.size-button {
  padding: 5px 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.size-button:hover {
  background-color: #f0f0f0;
}

.size-button.selected {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.preview-tips {
  font-size: 12px;
  color: #666;
  margin-top: 15px;
}

/* Design details styles */
.design-details {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ddd;
}

.design-name-container,
.quantity-container {
  margin-bottom: 15px;
}

.design-name-container label,
.quantity-container label {
  display: block;
  margin-bottom: 5px;
}

.design-name-container input,
.quantity-container input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.price-container {
  margin-bottom: 20px;
}

.total-price {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.save-button,
.add-to-cart-button {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;
}

.save-button {
  background-color: #52c41a;
  color: white;
}

.save-button:hover {
  background-color: #73d13d;
}

.add-to-cart-button {
  background-color: #1890ff;
  color: white;
}

.add-to-cart-button:hover {
  background-color: #40a9ff;
}

.add-to-cart-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.success-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  margin-top: 15px;
}

.login-required-message {
  color: #faad14;
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
}
