/* Main Layout */
.App {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

/* Header Styles */
.App-header {
  background-color: white;
  padding: 1rem 1rem 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .header-content {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

.logo-container {
  order: 1;
}

.slogan {
  order: 2;
}

.logo {
  height: 120px;
  width: auto;
  margin-bottom: 10px;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.slogan {
  font-size: 1.8rem;
  color: #2E7D32;
  margin-bottom: 10px;
  font-weight: 600;
  text-align: center;
}

.tagline {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 15px;
  font-style: italic;
  text-align: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  order: 0;
  padding: 10px;
}

.user-info .auth-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-info .design-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

/* Make buttons full width on mobile */
@media (max-width: 768px) {
  .user-info {
    position: relative;
    width: 100%;
    margin-top: 15px;
    align-items: center;
  }

  .user-info .auth-buttons {
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 8px;
  }

  .user-info .design-buttons {
    width: 100%;
    max-width: 300px;
    align-items: center;
    margin-top: 10px;
  }

  .tshirt-designer-button,
  .mug-designer-button,
  .login-button,
  .dashboard-button,
  .admin-button,
  .logout-button {
    width: 100%;
    max-width: 300px;
  }

  .user-info span {
    margin-bottom: 8px;
  }
}

/* Button Styles */
.login-button,
.signup-button,
.logout-button,
.dashboard-button,
.admin-button,
.admin-login-link,
.tshirt-designer-button,
.mug-designer-button {
  padding: 0.5rem 1rem;
  border-radius: 0;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 5px;
}

.login-button {
  background-color: #007bff;
  color: white;
}

.signup-button {
  background-color: #2E7D32;
  color: white;
  margin-right: 10px;
}

.signup-button:hover {
  background-color: #1B5E20;
}

.admin-login-link {
  background-color: transparent;
  color: #2e7d32;
  border: 1px solid #2e7d32;
  margin-left: 10px;
}

.dashboard-button {
  background-color: #e3f2fd;
  color: #1976D2;
  border: 1px solid #bbdefb;
}

.admin-button {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.login-button:hover {
  background-color: #0056b3;
}

.admin-login-link:hover {
  background-color: #e8f5e9;
}

.admin-login-hint {
  margin-left: 10px;
  font-size: 12px;
  color: #666;
}

.dashboard-button:hover {
  background-color: #bbdefb;
}

.admin-button:hover {
  background-color: #c8e6c9;
}

.tshirt-designer-button {
  background-color: #f8bbd0;
  color: #880e4f;
  border: 1px solid #f48fb1;
  width: 150px;
  text-align: center;
  white-space: nowrap;
  font-size: 0.9rem;
}

.tshirt-designer-button:hover {
  background-color: #f48fb1;
}

.mug-designer-button {
  background-color: #bbdefb;
  color: #0d47a1;
  border: 1px solid #90caf9;
  width: 150px;
  text-align: center;
  white-space: nowrap;
  font-size: 0.9rem;
}

.mug-designer-button:hover {
  background-color: #90caf9;
}

/* Order Limit Notification */
.order-limit-notification {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  border: 1px solid #f5c6cb;
}

/* Site Notification */
.site-notification {
  background-color: #fff3cd;
  color: #856404;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  border: 1px solid #ffeeba;
}

.logout-button {
  background-color: #dc3545;
  color: white;
}

.logout-button:hover {
  background-color: #c82333;
}

/* Main Content */
.main-content {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.order-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-hint {
  color: #666;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Update the form-group select styling */
.form-group select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0;
  font-size: 1rem;
  background-color: white;
  height: 42px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  cursor: pointer;
}

/* Location Selection Styles */
.location-selection {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Assisted Provinces Styles */
.assisted-provinces-info {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #e3f2fd;
  border-radius: 0;
  border-left: 3px solid #1976D2;
  width: 100%;
  box-sizing: border-box;
}

.assisted-provinces-info p {
  margin: 0 0 0.5rem;
  font-weight: 500;
  color: #1976D2;
  width: 100%;
}

.helper-text {
  font-weight: normal;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
  margin-left: 5px;
}

.assisted-provinces-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

.assisted-province-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 10px;
  background-color: #bbdefb;
  color: #0d47a1;
  border-radius: 0;
  font-size: 0.85rem;
  border: none;
  cursor: pointer;
  margin: 2px;
  transition: all 0.2s ease;
  font-family: inherit;
  min-height: 24px;
  text-align: center;
}

.assisted-province-tag:hover {
  background-color: #90caf9;
  color: #002171;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.assisted-province-tag:active {
  background-color: #64b5f6;
  transform: translateY(1px);
}

.assisted-province-tag:focus {
  outline: 2px solid #1976D2;
  outline-offset: 1px;
}

.assisted-province-tag.selected {
  background-color: #64b5f6;
  color: #002171;
  font-weight: bold;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Style for assisted provinces in dropdown */
option.assisted-province-option {
  font-weight: bold;
  background-color: #e3f2fd;
}

/* Highlight effect for dropdowns */
@keyframes highlight-pulse {
  0% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(25, 118, 210, 0); }
  100% { box-shadow: 0 0 0 0 rgba(25, 118, 210, 0); }
}

.highlight-selection {
  animation: highlight-pulse 1.5s ease-out;
  border-color: #1976D2 !important;
  background-color: #e3f2fd !important;
}

.select-container {
  margin-bottom: 1rem;
}

.sub-label {
  font-size: 0.9rem;
  color: #495057;
  margin-bottom: 0.25rem;
  font-weight: normal;
}

.region-select,
.province-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0;
  font-size: 1rem;
  background-color: white;
  margin-top: 0.25rem;
  height: 42px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  cursor: pointer;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Print Type Selection */
.print-options {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.print-option {
  flex: 1;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
}

.print-option.selected {
  border-color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

/* File Upload Area */
.file-upload {
  border: 2px dashed #ddd;
  padding: 2rem;
  text-align: center;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-upload:hover {
  border-color: #1976D2;
  background-color: rgba(25, 118, 210, 0.05);
}

/* File Input Styling */
.file-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2rem auto;
  max-width: 600px;
  text-align: center;
  clear: both;
  width: 100%;
  order: 3;
}

.file-upload-container h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
  text-align: center;
}

.file-upload-container p {
  margin-bottom: 1.5rem;
  color: #666;
  text-align: center;
}

.file-upload-container .file-input-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
  cursor: pointer;
}

.file-upload-container .file-input-wrapper input[type="file"] {
  position: absolute;
  left: -9999px;
  opacity: 0;
  cursor: pointer;
  width: 1px;
  height: 1px;
}

.file-upload-container .file-input-button {
  display: inline-block;
  padding: 12px 24px;
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 0;
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.file-upload-container .file-input-button:hover {
  background-color: #1565C0;
}

.file-upload-container .file-types {
  margin-top: 0.75rem;
  font-size: 0.85rem;
  color: #666;
}

input[type="file"] {
  padding: 10px;
  border: 1px solid #ddd;
  background-color: #f8f9fa;
  border-radius: 0;
  width: 100%;
  cursor: pointer;
  display: none;
}

input[type="file"]:focus {
  outline: none;
  border-color: #1976D2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.25);
}

.file-list {
  margin-top: 1.5rem;
  width: 100%;
  max-width: 600px;
}

.file-list h3 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #333;
  border-bottom: 2px solid #1976D2;
  padding-bottom: 0.5rem;
  display: inline-block;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-radius: 0;
  margin-bottom: 0.5rem;
  border-left: 3px solid #1976D2;
  flex-wrap: wrap;
}

.file-item span {
  flex: 1;
  font-weight: 500;
}

.file-item input[type="number"] {
  width: 60px;
  height: 36px;
  margin: 0 15px;
  padding: 0 10px;
  border: 1px solid #ddd;
  border-radius: 0;
  text-align: center;
}

.file-item button {
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 0;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  height: 36px;
  transition: background-color 0.2s ease;
}

.file-item button:hover {
  background-color: #b71c1c;
}

/* Order Summary */
.order-summary {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.total {
  font-weight: bold;
  border-top: 1px solid #ddd;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

/* Submit Button */
.submit-button {
  background-color: #28a745;
  color: white;
  padding: 1rem;
  border: none;
  border-radius: 4px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: 1rem;
}

.submit-button:hover {
  background-color: #218838;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Loading State */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  margin-top: 1rem;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

/* Responsive Design */
/* Tablet Responsive Styles */
@media (max-width: 768px) {
  .App {
    padding: 10px;
  }

  .main-content {
    padding: 1rem;
    max-width: 100%;
  }

  .header-content {
    gap: 0.5rem;
  }

  .logo {
    height: 90px;
  }

  .slogan {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .user-info {
    position: static;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 15px;
    margin-bottom: 15px;
    order: 4;
    width: 100%;
  }

  .user-info button {
    margin: 5px;
    min-width: 120px;
  }

  .file-upload-container {
    margin-top: 1rem;
    order: 3;
    padding: 0 10px;
  }

  .file-upload-container h2 {
    font-size: 1.4rem;
  }

  .print-options {
    flex-direction: column;
  }

  .file-item {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    text-align: left;
    gap: 0.5rem;
    padding: 12px;
  }

  .file-item span {
    width: 100%;
    margin-bottom: 8px;
  }

  .file-item .custom-number-input {
    margin: 0 5px 0 0;
  }

  /* Form improvements */
  .form-group {
    margin-bottom: 15px;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
  }

  .select-container {
    margin-bottom: 15px;
  }

  /* Responsive styles for confirmation dialog */
  .alert-modal {
    padding: 1.5rem;
    max-width: 95%;
  }

  .confirmation-title {
    font-size: 2rem;
  }

  .order-number {
    font-size: 1.4rem;
  }

  .confirmation-details {
    font-size: 1.2rem;
  }

  .thank-you-message {
    font-size: 1.3rem;
  }

  .close-button {
    font-size: 1.2rem;
    padding: 10px 20px;
    border-radius: 0;
  }

  /* Assisted provinces list */
  .assisted-provinces-info {
    padding: 12px;
    width: 100%;
    box-sizing: border-box;
    display: block;
  }

  .assisted-provinces-list {
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;
    display: flex;
    gap: 6px;
  }

  .assisted-province-tag {
    margin: 3px;
    flex: 0 0 auto;
  }

  /* Location selection */
  .location-selection {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 480px) {
  .App {
    padding: 5px;
  }

  .App-header {
    padding: 10px;
    margin-bottom: 1rem;
  }

  .logo {
    height: 70px;
  }

  .slogan {
    font-size: 0.9rem;
  }

  .main-content {
    padding: 15px;
  }

  .file-upload-container h2 {
    font-size: 1.2rem;
  }

  .file-upload-container p {
    font-size: 0.9rem;
  }

  .file-input-button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .file-item {
    padding: 10px;
  }

  .file-item button {
    padding: 6px 12px;
    font-size: 0.8rem;
    height: 32px;
  }

  .form-group label {
    font-size: 0.9rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .submit-button {
    padding: 12px;
    font-size: 1rem;
  }

  .order-summary {
    padding: 12px;
  }

  .summary-item {
    font-size: 0.9rem;
  }

  /* Footer adjustments */
  .app-footer {
    padding: 1rem 0;
    margin-top: 2rem;
  }

  .footer-content {
    font-size: 0.8rem;
    padding: 0 10px;
  }

  .admin-dot-link {
    padding: 0 4px;
  }

  /* Assisted provinces mobile styles */
  .assisted-provinces-info {
    padding: 10px;
    margin-bottom: 1rem;
    width: 100%;
    box-sizing: border-box;
    display: block;
  }

  .assisted-provinces-info p {
    font-size: 0.85rem;
    width: 100%;
    display: block;
  }

  .helper-text {
    font-size: 0.75rem;
    display: block;
    margin-left: 0;
    margin-top: 2px;
    width: 100%;
  }

  .assisted-provinces-list {
    gap: 4px;
    margin-top: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    width: 100%;
  }

  .assisted-province-tag {
    padding: 5px 8px;
    font-size: 0.75rem;
    margin: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
  }

  .location-selection {
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
  }
}

/* Error Messages */
.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.75rem;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 8px 12px;
  border-left: 3px solid #dc3545;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Success Messages */
.success-message {
  color: #28a745;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  position: relative;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-modal {
  width: 100%;
  max-width: 500px;
  padding: 0 !important;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s ease-out forwards;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.auth-header h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.auth-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.modal-close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 5px;
  line-height: 1;
  transition: color 0.2s ease;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.modal-close-button:hover {
  color: #1976D2;
}

/* Province Selection */
.delivery-info {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #007bff;
  font-weight: 500;
}

/* Alert Modal */
.alert-modal {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 90%;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.alert-content {
  text-align: center;
  padding: 1rem;
}

.confirmation-title {
  font-size: 2.5rem;
  color: #2E7D32;
  margin-bottom: 1.5rem;
}

.order-number {
  background-color: #e3f2fd;
  padding: 10px;
  border-radius: 4px;
  display: inline-block;
  margin: 1rem 0;
  font-size: 1.8rem;
}

.order-number strong {
  color: #1976D2;
}

.confirmation-details {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin: 1rem 0;
  font-size: 1.4rem;
}

.email-address {
  color: #1976D2;
  font-weight: bold;
  margin: 0.5rem 0;
}

.email-note {
  color: #e65100;
  font-size: 1rem;
  margin: 0.5rem 0;
}

.thank-you-message {
  color: #2E7D32;
  font-weight: 500;
  margin: 1.5rem 0;
  font-size: 1.6rem;
}

/* Confirmation Dialog Close Button */
.close-button {
  padding: 12px 24px;
  font-size: 1.4rem;
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: auto;
  min-width: 150px;
  margin: 0 auto;
  display: block;
}

/* Admin Access Page Styles */
.admin-access-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2rem auto;
  max-width: 600px;
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-left: 4px solid #2E7D32;
}

.admin-access-container h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #2E7D32;
}

.admin-access-container p {
  margin-bottom: 2rem;
  color: #555;
  line-height: 1.5;
}

.admin-login-button {
  padding: 12px 30px;
  font-size: 1.1rem;
  background-color: #2E7D32;
  color: white;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.admin-login-button:hover {
  background-color: #1B5E20;
}

.back-to-home-button {
  padding: 8px 20px;
  font-size: 0.9rem;
  background-color: transparent;
  color: #555;
  border: 1px solid #ccc;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-to-home-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

/* Footer Styles */
.app-footer {
  margin-top: 3rem;
  padding: 2.5rem 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  position: relative;
  width: 100%;
  clear: both;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  color: #6c757d;
  font-size: 0.9rem;
  padding: 20px 0;
}

.footer-sections {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30px;
  text-align: left;
}

.footer-section {
  flex: 1;
  min-width: 200px;
  margin: 0 15px 20px;
}

.footer-section h3 {
  color: #2E7D32;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.footer-section p {
  line-height: 1.5;
  margin-bottom: 10px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
  position: relative;
  padding-left: 15px;
}

.footer-section li:before {
  content: '✓';
  color: #2E7D32;
  position: absolute;
  left: 0;
}

.footer-bottom {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
  text-align: center;
}

.admin-dot-link {
  cursor: pointer;
  padding-left: 2px;
  padding-right: 2px;
  font-weight: bold;
  display: inline-block;
}

.admin-dot-link:hover {
  color: #6c757d;
}



.close-button:hover {
  background-color: #1565C0;
}

.close-button:active {
  background-color: #0D47A1;
  transform: translateY(1px);
}

/* Submit Button Styles */
.submit-button {
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 0;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  cursor: pointer;
  width: 100%;
  margin-top: 1rem;
  transition: background-color 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.submit-button:hover {
  background-color: #1565C0;
}

.submit-button:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
  opacity: 0.7;
}
