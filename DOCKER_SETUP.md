# Docker Setup for PixelPrints App

This guide provides instructions for setting up and running the PixelPrints application using Docker.

## Prerequisites

- Docker and Docker Compose installed on your machine
- Git repository cloned to your local machine

## Components

The application consists of three Docker containers:

1. **Frontend** - React application served by Nginx (Alpine-based)
2. **Backend** - Node.js Express API
3. **Database** - PostgreSQL database

## Building and Running the Application

### 1. Build and Start the Containers

```bash
# Build and start all containers in detached mode
docker-compose up --build -d
```

### 2. Initialize the Database

#### On Linux/Mac:
```bash
# Make the script executable
chmod +x init-db.sh

# Run the initialization script
./init-db.sh
```

#### On Windows (PowerShell):
```powershell
# Run the initialization script
.\init-db.ps1
```

### 3. Access the Application

- Frontend: http://localhost
- Backend API: http://localhost:3001
- API Test: http://localhost:3001/api/test

## Stopping the Application

```bash
# Stop all containers
docker-compose down
```

To stop and remove all containers, networks, and volumes:

```bash
# Stop and remove containers, networks, and volumes
docker-compose down -v
```

## Testing Individual Containers

### Testing the Frontend Container

```bash
# Build and run only the frontend container
docker-compose up --build -d frontend

# Check the logs
docker-compose logs -f frontend
```

### Testing the Backend Container

```bash
# Build and run the backend and database containers
docker-compose up --build -d backend

# Check the logs
docker-compose logs -f backend
```

### Testing the Database Container

```bash
# Build and run only the database container
docker-compose up --build -d db

# Connect to the database using psql
docker-compose exec db psql -U postgres -d pixel_prints

# Run some test queries
# SELECT * FROM orders;
# \q (to quit)
```

## Environment Variables

The application uses the following environment variables:

### Frontend
- `REACT_APP_API_URL`: URL of the backend API

### Backend
- `NODE_ENV`: Environment mode (development/production)
- `FRONTEND_URL`: URL of the frontend application (for CORS)
- `JWT_SECRET`: Secret key for JWT authentication
- `ADMIN_EMAIL`: Default admin email
- `ADMIN_PASSWORD`: Default admin password
- `EMAIL_USER`: Email for sending notifications
- `EMAIL_PASS`: Password for the email account
- `DATABASE_URL`: PostgreSQL connection string

### Database
- `POSTGRES_USER`: PostgreSQL username
- `POSTGRES_PASSWORD`: PostgreSQL password
- `POSTGRES_DB`: PostgreSQL database name

## Troubleshooting

### Database Connection Issues

If the backend cannot connect to the database, try:

1. Check if the database container is running:
   ```bash
   docker-compose ps
   ```

2. Check the database logs:
   ```bash
   docker-compose logs db
   ```

3. Try to connect to the database manually:
   ```bash
   docker-compose exec db psql -U postgres -d pixel_prints
   ```

### Frontend Cannot Connect to Backend

If the frontend cannot connect to the backend, check:

1. The `REACT_APP_API_URL` environment variable in the frontend container
2. The CORS configuration in the backend
3. Network connectivity between containers

### Container Build Failures

If container builds fail, check:

1. Docker and Docker Compose versions
2. Disk space
3. Build logs for specific errors
