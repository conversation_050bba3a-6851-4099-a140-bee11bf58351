# Setting Up GitHub Actions for Automated Deployment

This guide explains how to set up GitHub Actions to automatically deploy your application to Render and Netlify.

## Prerequisites

1. GitHub repository with your code
2. Render account and application
3. Netlify account and site

## Setting Up Secrets

You need to add the following secrets to your GitHub repository:

1. Go to your GitHub repository
2. Click on "Settings"
3. <PERSON>lick on "Secrets and variables" > "Actions"
4. <PERSON>lick on "New repository secret"
5. Add the following secrets:

### For Render Deployment

| Name | Description |
|------|-------------|
| `RENDER_DEPLOY_HOOK_URL` | Your Render deploy hook URL (find it in your Render service settings) |
| `RENDER_APP_URL` | The URL of your Render application (e.g., https://your-service-name.onrender.com) |

### For Netlify Deployment

| Name | Description |
|------|-------------|
| `NETLIFY_AUTH_TOKEN` | Your Netlify personal access token |
| `NETLIFY_SITE_ID` | The API ID of your Netlify site |

## How to Get the Required Values

### Render Deploy Hook URL

1. Log in to your [Render Dashboard](https://dashboard.render.com/)
2. Click on your web service
3. Go to the "Settings" tab
4. Scroll down to the "Deploy Hooks" section
5. Click "Add Deploy Hook"
6. Enter a name for the hook (e.g., "GitHub Actions")
7. Click "Create Hook"
8. Copy the generated URL

### Render App URL

This is the URL of your Render application, typically in the format `https://your-service-name.onrender.com`.

### Netlify Auth Token

1. Log in to your [Netlify account](https://app.netlify.com/)
2. Click on your profile picture in the top right
3. Click on "User settings"
4. Click on "Applications"
5. Scroll down to "Personal access tokens"
6. Click "New access token"
7. Give it a description and click "Generate token"

### Netlify Site ID

1. Log in to your [Netlify account](https://app.netlify.com/)
2. Click on your site
3. Go to "Site settings"
4. Scroll down to "Site information"
5. Copy the "API ID" value

## Workflow Files

The workflow files are already set up in the `.github/workflows` directory:

- `render-deploy.yml`: Deploys the backend to Render
- `netlify-deploy.yml`: Deploys the frontend to Netlify

These workflows will run automatically when you push changes to the specified branches and paths.

## Manual Trigger

You can also manually trigger the workflows:

1. Go to the "Actions" tab in your GitHub repository
2. Select the workflow you want to run
3. Click "Run workflow"
4. Select the branch and click "Run workflow"

## Troubleshooting

If the workflows fail, check the following:

1. Make sure all the secrets are correctly set up
2. Check the workflow logs for error messages
3. Verify that your Render and Netlify accounts have the necessary permissions
4. Ensure that your application code is compatible with the deployment platforms
