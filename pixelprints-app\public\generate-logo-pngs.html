<!DOCTYPE html>
<html>
<head>
    <title>Generate Pixel Prints Logo PNGs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
        .buttons {
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            margin: 0 10px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generate Pixel Prints Logo PNGs</h1>
        <p>This tool creates PNG versions of the Pixel Prints logo.</p>
        
        <div>
            <h2>192x192 Logo</h2>
            <canvas id="canvas192" width="192" height="192"></canvas>
        </div>
        
        <div>
            <h2>512x512 Logo</h2>
            <canvas id="canvas512" width="512" height="512"></canvas>
        </div>
        
        <div class="buttons">
            <button id="download192">Download logo192.png</button>
            <button id="download512">Download logo512.png</button>
        </div>
    </div>

    <script>
        // Function to draw the Pixel Prints logo on a canvas
        function drawLogo(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Calculate scaling factor
            const scale = size / 150;
            
            // Center the logo
            const centerX = size / 2;
            const centerY = size / 2;
            
            // Draw white background
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, size, size);
            
            // Printer top (gray)
            ctx.fillStyle = '#c0c0c0';
            const topWidth = 100 * scale;
            const topHeight = 15 * scale;
            const topX = centerX - (topWidth / 2);
            const topY = centerY - 40 * scale;
            ctx.beginPath();
            ctx.roundRect(topX, topY, topWidth, topHeight, 2 * scale);
            ctx.fill();
            
            // Printer body (blue)
            ctx.fillStyle = '#4a90e2';
            const bodyHeight = 50 * scale;
            ctx.beginPath();
            ctx.roundRect(topX, topY + topHeight, topWidth, bodyHeight, 3 * scale);
            ctx.fill();
            
            // Printer output tray (dark gray)
            ctx.fillStyle = '#333333';
            const trayWidth = 80 * scale;
            const trayHeight = 10 * scale;
            const trayX = centerX - (trayWidth / 2);
            ctx.beginPath();
            ctx.roundRect(trayX, topY + topHeight + bodyHeight, trayWidth, trayHeight, 1 * scale);
            ctx.fill();
            
            // Printer button (white)
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(topX + topWidth - 15 * scale, topY + topHeight + 15 * scale, 3 * scale, 0, Math.PI * 2);
            ctx.fill();
            
            // Text
            ctx.fillStyle = '#000000';
            ctx.font = `bold ${14 * scale}px monospace`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('PIXEL PRINTS', centerX, topY + topHeight + bodyHeight + trayHeight + 20 * scale);
        }
        
        // Draw logos on both canvases
        drawLogo('canvas192', 192);
        drawLogo('canvas512', 512);
        
        // Set up download buttons
        document.getElementById('download192').addEventListener('click', function() {
            const canvas = document.getElementById('canvas192');
            const link = document.createElement('a');
            link.download = 'logo192.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });
        
        document.getElementById('download512').addEventListener('click', function() {
            const canvas = document.getElementById('canvas512');
            const link = document.createElement('a');
            link.download = 'logo512.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });
    </script>
</body>
</html>
