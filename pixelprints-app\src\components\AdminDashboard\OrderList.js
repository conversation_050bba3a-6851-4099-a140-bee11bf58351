import React, { useState } from 'react';
import OrderDetails from './OrderDetails';

const OrderList = ({ orders, updateOrderStatus, deleteOrder }) => {
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');

  // Filter orders by status
  const filteredOrders = filterStatus === 'all'
    ? orders
    : orders.filter(order => order.status === filterStatus);

  // View order details
  const viewOrderDetails = (order) => {
    setSelectedOrder(order);
  };

  // Close order details modal
  const closeOrderDetails = () => {
    setSelectedOrder(null);
  };

  // Confirm order deletion
  const confirmDelete = (orderId) => {
    setDeleteConfirmation(orderId);
  };

  // Cancel order deletion
  const cancelDelete = () => {
    setDeleteConfirmation(null);
  };

  // Proceed with order deletion
  const proceedWithDelete = (orderId) => {
    deleteOrder(orderId);
    setDeleteConfirmation(null);
  };

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <div className="order-list">
      <div className="filter-controls">
        <label htmlFor="status-filter">Filter by status:</label>
        <select
          id="status-filter"
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
        >
          <option value="all">All Orders</option>
          <option value="pending">Pending</option>
          <option value="processing">Processing</option>
          <option value="completed">Completed</option>
          <option value="delayed">Delayed</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>

      {filteredOrders.length === 0 ? (
        <p className="no-orders">No orders found.</p>
      ) : (
        <table>
          <thead>
            <tr>
              <th>Order #</th>
              <th>Customer</th>
              <th>Date</th>
              <th>Type</th>
              <th>Price</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredOrders.map(order => (
              <tr key={order.id}>
                <td>{order.order_number}</td>
                <td>{order.customer_name}</td>
                <td>{formatDate(order.created_at)}</td>
                <td>{order.print_type}</td>
                <td>₱{parseFloat(order.price || 0).toFixed(2)}</td>
                <td>
                  <span className={`status-badge ${order.status}`}>
                    {order.status}
                  </span>
                </td>
                <td className="order-actions">
                  <button
                    className="view-button"
                    onClick={() => viewOrderDetails(order)}
                  >
                    View
                  </button>

                  {order.status === 'pending' && (
                    <button
                      className="complete-button"
                      onClick={() => updateOrderStatus(order.id, 'completed')}
                    >
                      Complete
                    </button>
                  )}

                  {order.status === 'completed' && (
                    <button
                      className="complete-button"
                      onClick={() => updateOrderStatus(order.id, 'pending')}
                    >
                      Reopen
                    </button>
                  )}

                  {deleteConfirmation === order.id ? (
                    <>
                      <button
                        className="delete-button"
                        onClick={() => proceedWithDelete(order.id)}
                      >
                        Confirm
                      </button>
                      <button
                        className="view-button"
                        onClick={cancelDelete}
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <button
                      className="delete-button"
                      onClick={() => confirmDelete(order.id)}
                    >
                      Delete
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          onClose={closeOrderDetails}
          updateOrderStatus={updateOrderStatus}
        />
      )}
    </div>
  );
};

export default OrderList;
