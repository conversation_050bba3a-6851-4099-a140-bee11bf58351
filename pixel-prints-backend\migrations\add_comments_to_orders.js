const db = require('../db');

async function addCommentsToOrders() {
  try {
    // Check if comments column already exists
    const checkResult = await db.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'orders' AND column_name = 'comments'
    `);

    if (checkResult.rows.length === 0) {
      // Add comments column as a JSON array
      await db.query(`
        ALTER TABLE orders 
        ADD COLUMN comments JSONB DEFAULT '[]'::jsonb
      `);
      console.log('Added comments column to orders table');
    } else {
      console.log('Comments column already exists in orders table');
    }
  } catch (error) {
    console.error('Error adding comments column:', error);
  }
}

// Run the migration
addCommentsToOrders()
  .then(() => {
    console.log('Migration completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
