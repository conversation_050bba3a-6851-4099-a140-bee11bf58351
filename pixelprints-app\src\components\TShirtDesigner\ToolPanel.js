import React from 'react';

const ToolPanel = ({
  currentTool,
  setCurrentTool,
  brushSize,
  setBrushSize,
  brushColor,
  showColorPicker,
  setShowColorPicker,
  addText,
  deleteSelected,
  clearCanvas,
}) => {
  return (
    <div className="tool-panel">
      <h3>Design Tools</h3>
      
      <div className="tool-buttons">
        <button
          className={`tool-button ${currentTool === 'select' ? 'active' : ''}`}
          onClick={() => setCurrentTool('select')}
          title="Select Tool"
        >
          <i className="fas fa-mouse-pointer"></i>
          <span>Select</span>
        </button>
        
        <button
          className={`tool-button ${currentTool === 'brush' ? 'active' : ''}`}
          onClick={() => setCurrentTool('brush')}
          title="Brush Tool"
        >
          <i className="fas fa-paint-brush"></i>
          <span>Brush</span>
        </button>
        
        <button
          className="tool-button"
          onClick={addText}
          title="Add Text"
        >
          <i className="fas fa-font"></i>
          <span>Add Text</span>
        </button>
        
        <button
          className="tool-button"
          onClick={deleteSelected}
          title="Delete Selected"
        >
          <i className="fas fa-trash"></i>
          <span>Delete</span>
        </button>
        
        <button
          className="tool-button"
          onClick={clearCanvas}
          title="Clear Canvas"
        >
          <i className="fas fa-trash-alt"></i>
          <span>Clear All</span>
        </button>
      </div>
      
      {currentTool === 'brush' && (
        <div className="brush-controls">
          <div className="brush-size-control">
            <label htmlFor="brush-size">Brush Size:</label>
            <input
              id="brush-size"
              type="range"
              min="1"
              max="50"
              value={brushSize}
              onChange={(e) => setBrushSize(parseInt(e.target.value))}
            />
            <span>{brushSize}px</span>
          </div>
        </div>
      )}
      
      <div className="color-control">
        <label>Color:</label>
        <div
          className="color-preview"
          style={{ backgroundColor: brushColor }}
          onClick={() => setShowColorPicker(!showColorPicker)}
        ></div>
      </div>
      
      <div className="tool-tips">
        <p><strong>Tips:</strong></p>
        <ul>
          <li>Use Select tool to move and resize objects</li>
          <li>Double-click text to edit</li>
          <li>Use Brush tool to draw freely</li>
        </ul>
      </div>
    </div>
  );
};

export default ToolPanel;
