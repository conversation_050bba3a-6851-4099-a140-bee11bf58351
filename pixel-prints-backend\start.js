const { spawn } = require('child_process');
const path = require('path');
require('dotenv').config();

// Function to run a script and return a promise
function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    console.log(`Running script: ${scriptPath}`);
    const process = spawn('node', [scriptPath], { stdio: 'inherit' });
    
    process.on('close', (code) => {
      if (code !== 0) {
        console.warn(`Script ${scriptPath} exited with code ${code}`);
      }
      resolve();
    });
    
    process.on('error', (err) => {
      console.error(`Error running script ${scriptPath}:`, err);
      resolve(); // Resolve anyway to continue with other scripts
    });
  });
}

// Main function to initialize the database and start the server
async function startApp() {
  try {
    // Only run initialization scripts if DATABASE_URL is available
    if (process.env.DATABASE_URL) {
      console.log('Database URL found, initializing database...');
      
      // Run initialization scripts
      await runScript(path.join(__dirname, 'db', 'init.js'));
      await runScript(path.join(__dirname, 'db', 'setup_settings.js'));
      await runScript(path.join(__dirname, 'db', 'create_admin.js'));
      await runScript(path.join(__dirname, 'db', 'add_site_notification_settings.js'));
      await runScript(path.join(__dirname, 'db', 'update_users_reset_token.js'));
      
      console.log('Database initialization completed');
    } else {
      console.log('No DATABASE_URL found, skipping database initialization');
    }
    
    // Start the server
    console.log('Starting server...');
    require('./server.js');
  } catch (error) {
    console.error('Error during startup:', error);
    process.exit(1);
  }
}

// Start the application
startApp();
