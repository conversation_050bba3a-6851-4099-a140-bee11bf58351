import React from 'react';

function Logo() {
  // Modern printer logo with "PIXEL PRINTS" text based on the provided design
  return (
    <div className="logo-container">
      <svg
        width="200"
        height="150"
        viewBox="0 0 200 150"
        xmlns="http://www.w3.org/2000/svg"
        className="logo"
        role="img"
        aria-labelledby="logoTitle logoDesc"
      >
        <title id="logoTitle">Pixel Prints Logo</title>
        <desc id="logoDesc">A modern printer logo for Pixel Prints professional printing services</desc>

        {/* Printer top (gray) */}
        <rect x="50" y="20" width="100" height="15" fill="#c0c0c0" rx="2" />

        {/* Printer body (blue) */}
        <rect x="50" y="35" width="100" height="50" fill="#4a90e2" rx="3" />

        {/* Printer output tray (dark gray) */}
        <rect x="60" y="85" width="80" height="10" fill="#333333" rx="1" />

        {/* Printer button (white) */}
        <circle cx="135" cy="50" r="3" fill="#ffffff" />

        {/* Text */}
        <text x="100" y="115" textAnchor="middle" fontFamily="monospace" fontWeight="bold" fontSize="14" fill="#000000">
          PIXEL PRINTS
        </text>
      </svg>
    </div>
  );
}

export default Logo;
