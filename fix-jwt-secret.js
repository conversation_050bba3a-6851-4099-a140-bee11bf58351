const fs = require('fs');
const path = require('path');

// Files to update
const filesToUpdate = [
  {
    path: path.join(__dirname, 'src', 'middleware', 'auth.js'),
    pattern: /const JWT_SECRET = process\.env\.JWT_SECRET \|\| ['"].*?['"]/,
    replacement: "const JWT_SECRET = process.env.JWT_SECRET || 'pixel-prints-secure-jwt-key-2024'"
  },
  {
    path: path.join(__dirname, 'src', 'controllers', 'admin-auth.js'),
    pattern: /const JWT_SECRET = process\.env\.JWT_SECRET \|\| ['"].*?['"]/,
    replacement: "const JWT_SECRET = process.env.JWT_SECRET || 'pixel-prints-secure-jwt-key-2024'"
  },
  {
    path: path.join(__dirname, 'src', 'routes', 'auth.js'),
    pattern: /const JWT_SECRET = process\.env\.JWT_SECRET \|\| ['"].*?['"]/,
    replacement: "const JWT_SECRET = process.env.JWT_SECRET || 'pixel-prints-secure-jwt-key-2024'"
  },
  {
    path: path.join(__dirname, 'src', 'controllers', 'auth.js'),
    pattern: /const JWT_SECRET = process\.env\.JWT_SECRET \|\| ['"].*?['"]/,
    replacement: "const JWT_SECRET = process.env.JWT_SECRET || 'pixel-prints-secure-jwt-key-2024'"
  }
];

// Update each file
filesToUpdate.forEach(file => {
  try {
    if (fs.existsSync(file.path)) {
      let content = fs.readFileSync(file.path, 'utf8');
      
      // Check if the pattern exists in the file
      if (file.pattern.test(content)) {
        // Replace the pattern with the new JWT secret
        content = content.replace(file.pattern, file.replacement);
        
        // Write the updated content back to the file
        fs.writeFileSync(file.path, content, 'utf8');
        console.log(`Updated JWT secret in ${file.path}`);
      } else {
        console.log(`Pattern not found in ${file.path}`);
      }
    } else {
      console.log(`File not found: ${file.path}`);
    }
  } catch (error) {
    console.error(`Error updating ${file.path}:`, error);
  }
});

console.log('JWT secret standardization complete.');
