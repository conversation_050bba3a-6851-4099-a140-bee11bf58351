-- Database initialization script

-- Drop existing table if it exists
DROP TABLE IF EXISTS orders;

-- Create orders table with proper constraints
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100) NOT NULL,
    print_type VARCHAR(50) NOT NULL,
    total_pages INTEGER,
    price DECIMAL(10,2) NOT NULL,
    address TEXT NOT NULL,
    province VARCHAR(100),
    delivery_charge DECIMAL(10,2) DEFAULT 0,
    files JSON,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_customer_email ON orders(customer_email);
CREATE INDEX idx_created_at ON orders(created_at DESC);
CREATE INDEX idx_status ON orders(status);

-- Insert some test data
INSERT INTO orders (
    order_number,
    customer_name,
    customer_email,
    print_type,
    total_pages,
    price,
    address,
    province,
    delivery_charge,
    files,
    status
) VALUES
(
    'ORD-001',
    'Test Customer',
    '<EMAIL>',
    'black',
    10,
    100.00,
    '123 Test Street',
    'Test Province',
    50.00,
    '[{"name": "test.pdf", "pages": 10, "copies": 1}]',
    'pending'
);

-- Create index on order_number for faster lookups
CREATE INDEX idx_order_number ON orders(order_number);

-- End of initialization script
