import { render, screen, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import ForgotPassword from './ForgotPassword';

// Mock the useNavigate hook
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ message: 'If your email is registered, you will receive a password reset link' }),
  })
);

describe('ForgotPassword Component', () => {
  beforeEach(() => {
    // Reset mock function calls before each test
    fetch.mockClear();
  });

  test('renders forgot password form', () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );
    
    // Check if form elements are present
    expect(screen.getByText('Forgot Password')).toBeInTheDocument();
    expect(screen.getByText('Reset your password')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByText('Send Reset Link')).toBeInTheDocument();
    expect(screen.getByText('Back to Home')).toBeInTheDocument();
  });

  test('validates email format', async () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );
    
    // Enter invalid email and submit form
    fireEvent.change(screen.getByLabelText('Email Address'), { target: { value: 'invalid-email' } });
    fireEvent.click(screen.getByText('Send Reset Link'));
    
    // Check if validation error is displayed
    expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
    
    // Verify that fetch was not called
    expect(fetch).not.toHaveBeenCalled();
  });

  test('submits form with valid email', async () => {
    render(
      <BrowserRouter>
        <ForgotPassword />
      </BrowserRouter>
    );
    
    // Enter valid email and submit form
    fireEvent.change(screen.getByLabelText('Email Address'), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByText('Send Reset Link'));
    
    // Verify that fetch was called with correct arguments
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/forgot-password'),
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
        body: JSON.stringify({ email: '<EMAIL>' }),
      })
    );
  });
});
